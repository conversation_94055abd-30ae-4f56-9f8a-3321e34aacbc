<?php

namespace Database\Seeders;

use App\Models\Earning;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class EarningSeeder extends Seeder
{
    public function run(): void
    {
        // Get professional users
        $professional = User::where('email', '<EMAIL>')->first();
        $proSeller = User::where('email', '<EMAIL>')->first();
        
        if (!$professional || !$proSeller) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }
        
        // Get projects
        $projects = Project::all();
        
        if ($projects->isEmpty()) {
            $this->command->info('No projects found. Please run ProjectSeeder first.');
            return;
        }
        
        // Create earnings for the first professional
        // Project-based earnings
        foreach ($projects->where('professional_id', $professional->id) as $index => $project) {
            $status = $index % 3 == 0 ? 'Pending' : ($index % 3 == 1 ? 'Paid' : 'Cancelled');
            $paymentDate = $status == 'Paid' ? Carbon::now()->subDays(rand(1, 30)) : null;
            
            Earning::create([
                'professional_id' => $professional->id,
                'project_id' => $project->id,
                'amount' => $project->budget * 0.8, // 80% of project budget
                'description' => 'Payment for ' . $project->title,
                'status' => $status,
                'payment_date' => $paymentDate,
                'transaction_id' => $status == 'Paid' ? 'TRX' . strtoupper(substr(md5(rand()), 0, 10)) : null,
                'created_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
        
        // Additional earnings
        Earning::create([
            'professional_id' => $professional->id,
            'amount' => 750.00,
            'description' => 'Logo Design for Sunrise Bakery',
            'status' => 'Paid',
            'payment_date' => Carbon::now()->subDays(15),
            'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
            'created_at' => Carbon::now()->subDays(20),
        ]);
        
        Earning::create([
            'professional_id' => $professional->id,
            'amount' => 1200.00,
            'description' => 'Website Design for Local Restaurant',
            'status' => 'Paid',
            'payment_date' => Carbon::now()->subDays(45),
            'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
            'created_at' => Carbon::now()->subDays(50),
        ]);
        
        // Create earnings for the second professional
        // Project-based earnings
        foreach ($projects->where('professional_id', $proSeller->id) as $index => $project) {
            $status = $index % 3 == 0 ? 'Pending' : ($index % 3 == 1 ? 'Paid' : 'Cancelled');
            $paymentDate = $status == 'Paid' ? Carbon::now()->subDays(rand(1, 30)) : null;
            
            Earning::create([
                'professional_id' => $proSeller->id,
                'project_id' => $project->id,
                'amount' => $project->budget * 0.8, // 80% of project budget
                'description' => 'Payment for ' . $project->title,
                'status' => $status,
                'payment_date' => $paymentDate,
                'transaction_id' => $status == 'Paid' ? 'TRX' . strtoupper(substr(md5(rand()), 0, 10)) : null,
                'created_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
        
        // Additional earnings
        Earning::create([
            'professional_id' => $proSeller->id,
            'amount' => 2500.00,
            'description' => 'AI Integration for E-commerce Platform',
            'status' => 'Paid',
            'payment_date' => Carbon::now()->subDays(10),
            'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
            'created_at' => Carbon::now()->subDays(15),
        ]);
        
        Earning::create([
            'professional_id' => $proSeller->id,
            'amount' => 1800.00,
            'description' => 'Data Analysis for Market Research',
            'status' => 'Paid',
            'payment_date' => Carbon::now()->subDays(30),
            'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
            'created_at' => Carbon::now()->subDays(35),
        ]);
    }
}
