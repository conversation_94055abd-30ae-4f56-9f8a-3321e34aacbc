<?php

namespace Database\Seeders;

use App\Models\Favorite;
use App\Models\User;
use Illuminate\Database\Seeder;

class FavoriteSeeder extends Seeder
{
    public function run(): void
    {
        // Get users
        $client = User::where('email', '<EMAIL>')->first();
        $professional = User::where('email', '<EMAIL>')->first();
        $proSeller = User::where('email', '<EMAIL>')->first();
        
        if (!$client || !$professional || !$proSeller) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }
        
        // Create favorites
        Favorite::create([
            'user_id' => $client->id,
            'professional_id' => $professional->id,
        ]);
        
        Favorite::create([
            'user_id' => $client->id,
            'professional_id' => $proSeller->id,
        ]);
    }
}
