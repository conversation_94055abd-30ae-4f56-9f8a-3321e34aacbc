<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $user = Auth::guard($guard)->user();

                // Redirect based on account type
                switch ($user->account_type) {
                    case 'client':
                        return redirect()->route('dashboard.client');
                    case 'professional':
                        return redirect()->route('dashboard.professional');
                    case 'seller':
                        // If we have a specific seller dashboard route
                        if ($user->is_seller) {
                            return redirect()->route('dashboard.seller');
                        }
                        return redirect()->route('dashboard.client');
                    case 'admin':
                        return redirect()->route('dashboard.admin');
                    case 'superadmin':
                        return redirect()->route('dashboard.admin');
                    default:
                        return redirect()->route('dashboard.client');
                }

                return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
}
