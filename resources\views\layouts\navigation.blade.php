<header class="sticky top-0 z-50 w-full border-b bg-white">
    <div class="container mx-auto flex h-16 items-center justify-between px-4">
        <div class="flex items-center gap-6 md:gap-10">
            <a href="{{ route('home') }}" class="flex items-center space-x-2">
                <div class="relative h-8 w-8 overflow-hidden rounded">
                    <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                        <span class="text-[#fcefcc] font-bold text-lg">H</span>
                    </div>
                </div>
                <span class="hidden font-bold sm:inline-block">Hermosart</span>
            </a>
            <nav class="hidden md:flex md:gap-6">
                <a href="{{ route('home') }}"
                    class="text-sm font-medium transition-colors hover:text-[#710d17] {{ request()->routeIs('home') ? 'text-[#710d17] font-semibold' : 'text-zinc-700' }}">Home</a>
                <a href="{{ route('services') }}"
                    class="text-sm font-medium transition-colors hover:text-[#710d17] {{ request()->routeIs('services') ? 'text-[#710d17] font-semibold' : 'text-zinc-700' }}">Services</a>
                <a href="{{ route('talent') }}"
                    class="text-sm font-medium transition-colors hover:text-[#710d17] {{ request()->routeIs('talent') ? 'text-[#710d17] font-semibold' : 'text-zinc-700' }}">Talent</a>
                <a href="{{ route('stores') }}"
                    class="text-sm font-medium transition-colors hover:text-[#710d17] {{ request()->routeIs('stores') || request()->routeIs('store.show') ? 'text-[#710d17] font-semibold' : 'text-zinc-700' }}">Stores</a>
                <a href="{{ route('products') }}"
                    class="text-sm font-medium transition-colors hover:text-[#710d17] {{ request()->routeIs('products') ? 'text-[#710d17] font-semibold' : 'text-zinc-700' }}">Products</a>
            </nav>
        </div>
        <div class="flex items-center gap-2">
            @guest
                <a href="{{ route('login') }}"
                    class="hidden md:inline-flex items-center justify-center rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#fcefcc]/50">
                    Log in
                </a>
                <a href="{{ route('register') }}"
                    class="hidden md:inline-flex items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[#9a2c39]">
                    Register
                </a>
            @else
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center gap-2 text-sm font-medium text-zinc-700">
                        <span>{{ Auth::user()->name }}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-4 w-4">
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false"
                        class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5"
                        x-cloak>
                        @if (Auth::user()->account_type === 'client')
                            <a href="{{ route('dashboard.client') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Dashboard</a>
                            <a href="{{ route('dashboard.client.profile') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Profile</a>
                            <a href="{{ route('dashboard.client.settings') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Settings</a>
                        @elseif(Auth::user()->account_type === 'professional')
                            <a href="{{ route('dashboard.professional') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Dashboard</a>
                            <a href="{{ route('dashboard.professional.profile') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Profile</a>
                            <a href="{{ route('dashboard.professional.settings') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Settings</a>
                        @elseif(Auth::user()->account_type === 'admin' || Auth::user()->account_type === 'superadmin')
                            <a href="{{ route('admin.seller-applications.index') }}"
                                class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Dashboard</a>
                            <a href="#" class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-100">Settings</a>
                        @endif
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                class="block w-full px-4 py-2 text-left text-sm text-zinc-700 hover:bg-zinc-100">
                                Log out
                            </button>
                        </form>
                    </div>
                </div>
            @endguest
            <button type="button"
                class="inline-flex md:hidden items-center justify-center rounded-md p-2 text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900"
                aria-controls="mobile-menu" aria-expanded="false" onclick="toggleMobileMenu()">
                <span class="sr-only">Open main menu</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-6 w-6">
                    <line x1="4" x2="20" y1="12" y2="12" />
                    <line x1="4" x2="20" y1="6" y2="6" />
                    <line x1="4" x2="20" y1="18" y2="18" />
                </svg>
            </button>
        </div>
    </div>
    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="md:hidden hidden" id="mobile-menu">
        <div class="space-y-1 px-2 pb-3 pt-2">
            <a href="{{ route('home') }}"
                class="block rounded-md px-3 py-2 text-base font-medium {{ request()->routeIs('home') ? 'bg-zinc-100 text-[#710d17]' : 'text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900' }}">Home</a>
            <a href="{{ route('services') }}"
                class="block rounded-md px-3 py-2 text-base font-medium {{ request()->routeIs('services') ? 'bg-zinc-100 text-[#710d17]' : 'text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900' }}">Services</a>
            <a href="{{ route('talent') }}"
                class="block rounded-md px-3 py-2 text-base font-medium {{ request()->routeIs('talent') ? 'bg-zinc-100 text-[#710d17]' : 'text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900' }}">Talent</a>
            <a href="{{ route('stores') }}"
                class="block rounded-md px-3 py-2 text-base font-medium {{ request()->routeIs('stores') || request()->routeIs('store.show') ? 'bg-zinc-100 text-[#710d17]' : 'text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900' }}">Stores</a>
            <a href="{{ route('products') }}"
                class="block rounded-md px-3 py-2 text-base font-medium {{ request()->routeIs('products') ? 'bg-zinc-100 text-[#710d17]' : 'text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900' }}">Products</a>
            @guest
                <a href="{{ route('login') }}"
                    class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Log
                    in</a>
                <a href="{{ route('register') }}"
                    class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Register</a>
            @else
                @if (Auth::user()->account_type === 'client')
                    <a href="{{ route('dashboard.client') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Dashboard</a>
                    <a href="{{ route('dashboard.client.profile') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Profile</a>
                    <a href="{{ route('dashboard.client.settings') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Settings</a>
                @elseif(Auth::user()->account_type === 'professional')
                    <a href="{{ route('dashboard.professional') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Dashboard</a>
                    <a href="{{ route('dashboard.professional.profile') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Profile</a>
                    <a href="{{ route('dashboard.professional.settings') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Settings</a>
                @elseif(Auth::user()->account_type === 'admin' || Auth::user()->account_type === 'superadmin')
                    <a href="{{ route('admin.seller-applications.index') }}"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Dashboard</a>
                    <a href="#"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Profile</a>
                    <a href="#"
                        class="block rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">Settings</a>
                @endif
                <form method="POST" action="{{ route('logout') }}" class="block">
                    @csrf
                    <button type="submit"
                        class="w-full text-left rounded-md px-3 py-2 text-base font-medium text-zinc-700 hover:bg-zinc-100 hover:text-zinc-900">
                        Log out
                    </button>
                </form>
            @endguest
        </div>
    </div>
</header>

<script>
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.remove('hidden');
        } else {
            mobileMenu.classList.add('hidden');
        }
    }
</script>
