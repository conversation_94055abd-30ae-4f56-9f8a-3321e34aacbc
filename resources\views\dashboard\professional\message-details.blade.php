@extends('layouts.dashboard')

@section('title', 'Message Details')

@section('header', 'Message Details')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.professional') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.professional.projects') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.projects*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="{{ route('dashboard.professional.messages') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.messages*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="{{ route('dashboard.professional.portfolio') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.portfolio*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.portfolio*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Portfolio
        </a>
        <a href="{{ route('dashboard.professional.products.create') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.products*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.products*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Products
        </a>
        <a href="{{ route('dashboard.professional.services.create') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.services*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.services*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Services
        </a>
        <a href="{{ route('dashboard.professional.earnings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.earnings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Earnings
        </a>
    </nav>
</div>

@include('dashboard.professional.sidebar-account')
@endsection

@section('mobile_sidebar')
@include('dashboard.professional.mobile-sidebar')
@endsection

@section('content')
<div class="mb-4">
    <a href="{{ route('dashboard.professional.messages') }}" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Messages
    </a>
</div>

@if(session('success'))
<div class="mb-4 rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
        </div>
        <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
@endif

<div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- Message Header -->
    <div class="px-4 py-4 border-b border-gray-100">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <h1 class="text-lg font-semibold text-gray-900">
                {{ preg_replace('/^(Re: )+/', '', $message->subject) }}
            </h1>

            @if($message->project)
            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                {{ $message->project->title }}
            </span>
            @endif
        </div>

        <div class="mt-2 text-sm text-gray-500">
            Conversation with
            <span class="font-medium">
                {{ $message->sender_id == Auth::id() ? $message->recipient->name : $message->sender->name }}
            </span>
        </div>
    </div>

    <!-- Chat Messages -->
    <div class="px-4 py-4 h-[calc(100vh-24rem)] sm:h-[calc(100vh-20rem)] overflow-y-auto" id="chat-messages">
        <!-- Related Messages -->
        @foreach($relatedMessages as $relatedMessage)
        <div class="flex {{ $relatedMessage->sender_id == Auth::id() ? 'justify-end' : 'justify-start' }} mb-4">
            <div class="flex max-w-[75%] sm:max-w-md {{ $relatedMessage->sender_id == Auth::id() ? 'flex-row-reverse' : '' }}">
                <div class="flex-shrink-0 {{ $relatedMessage->sender_id == Auth::id() ? 'ml-2' : 'mr-2' }}">
                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                        <span class="text-xs">{{ substr($relatedMessage->sender_id == Auth::id() ? Auth::user()->name : $relatedMessage->sender->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="{{ $relatedMessage->sender_id == Auth::id() ? 'bg-[#710d17] text-white' : 'bg-gray-100 text-gray-800' }} rounded-lg p-3 shadow-sm">
                    <div class="text-sm whitespace-pre-line break-words">{{ $relatedMessage->content }}</div>
                    <div class="mt-1 text-xs {{ $relatedMessage->sender_id == Auth::id() ? 'text-gray-200' : 'text-gray-500' }} text-right">
                        {{ $relatedMessage->created_at->format('M d, g:i A') }}
                    </div>
                </div>
            </div>
        </div>
        @endforeach

        <!-- Current Message -->
        <div class="flex {{ $message->sender_id == Auth::id() ? 'justify-end' : 'justify-start' }} mb-4">
            <div class="flex max-w-[75%] sm:max-w-md {{ $message->sender_id == Auth::id() ? 'flex-row-reverse' : '' }}">
                <div class="flex-shrink-0 {{ $message->sender_id == Auth::id() ? 'ml-2' : 'mr-2' }}">
                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                        <span class="text-xs">{{ substr($message->sender_id == Auth::id() ? Auth::user()->name : $message->sender->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="{{ $message->sender_id == Auth::id() ? 'bg-[#710d17] text-white' : 'bg-gray-100 text-gray-800' }} rounded-lg p-3 shadow-sm">
                    <div class="text-sm whitespace-pre-line break-words">{{ $message->content }}</div>
                    <div class="mt-1 text-xs {{ $message->sender_id == Auth::id() ? 'text-gray-200' : 'text-gray-500' }} text-right">
                        {{ $message->created_at->format('M d, g:i A') }}
                    </div>
                </div>
            </div>
        </div>

        @if($relatedMessages->isEmpty() && $message->sender_id != Auth::id())
        <div class="text-center text-sm text-gray-500 my-4 py-2">
            <p>This is the start of your conversation with {{ $message->sender->name }}.</p>
        </div>
        @endif
    </div>

    <!-- Reply Section -->
    <div class="px-4 py-4 bg-gray-50 border-t border-gray-100">
        <form id="messageForm" action="{{ route('dashboard.professional.messages.store') }}" method="POST" class="space-y-3">
            @csrf
            <input type="hidden" name="recipient_id" value="{{ $message->sender_id == Auth::id() ? $message->recipient_id : $message->sender_id }}">
            <input type="hidden" name="subject" value="{{ preg_replace('/^(Re: )+/', 'Re: ', $message->subject) }}">
            <input type="hidden" name="message_id" value="{{ $message->id }}">
            @if($message->project_id)
            <input type="hidden" name="project_id" value="{{ $message->project_id }}">
            @endif

            <div class="flex items-end space-x-2">
                <div class="flex-grow">
                    <textarea
                        id="messageContent"
                        name="content"
                        rows="2"
                        class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-[#710d17] focus:border-[#710d17] text-sm"
                        required
                        placeholder="Type your message here..."
                    ></textarea>
                </div>
                <div>
                    <button
                        type="submit"
                        id="sendButton"
                        class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatMessages = document.getElementById('chat-messages');
        const messageForm = document.getElementById('messageForm');
        const messageContent = document.getElementById('messageContent');
        const sendButton = document.getElementById('sendButton');

        // Scroll to bottom of chat messages on page load
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Make Enter key send the message (Shift+Enter for new line)
        messageContent.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (messageContent.value.trim() !== '') {
                    sendButton.disabled = true;
                    sendButton.classList.add('opacity-75');
                    messageForm.submit();
                }
            }
        });

        // Submit form via AJAX to stay on the same page
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (messageContent.value.trim() === '') {
                return;
            }

            // Disable the send button to prevent double-sending
            sendButton.disabled = true;
            sendButton.classList.add('opacity-75');

            // Create FormData object
            const formData = new FormData(messageForm);

            // Send the message via AJAX
            fetch(messageForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add the new message to the chat
                    const newMessageHtml = `
                        <div class="flex justify-end mb-4">
                            <div class="flex max-w-xs sm:max-w-md flex-row-reverse">
                                <div class="flex-shrink-0 ml-2">
                                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                                        <span class="text-xs">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="bg-[#710d17] text-white rounded-lg p-3 shadow-sm">
                                    <div class="text-sm whitespace-pre-line">${messageContent.value}</div>
                                    <div class="mt-1 text-xs text-gray-200 text-right">
                                        Just now
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    chatMessages.insertAdjacentHTML('beforeend', newMessageHtml);

                    // Clear the textarea
                    messageContent.value = '';

                    // Scroll to the bottom of the chat
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    // Re-enable the send button
                    sendButton.disabled = false;
                    sendButton.classList.remove('opacity-75');
                } else {
                    alert('Failed to send message. Please try again.');
                    sendButton.disabled = false;
                    sendButton.classList.remove('opacity-75');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending the message. Please try again.');
                sendButton.disabled = false;
                sendButton.classList.remove('opacity-75');
            });
        });

        // Poll for new messages every 10 seconds
        function checkForNewMessages() {
            fetch(`/dashboard/professional/messages/check-new/${messageForm.querySelector('input[name="message_id"]').value}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.hasNewMessages) {
                    // Reload the messages container
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
        }

        // Check for new messages every 10 seconds
        setInterval(checkForNewMessages, 10000);
    });
</script>
@endsection
