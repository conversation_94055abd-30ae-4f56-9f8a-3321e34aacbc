<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('professionals', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->nullable();
            $table->string('title');
            $table->json('skills');
            $table->text('description')->nullable();
            $table->string('experience')->nullable();
            $table->decimal('rating', 3, 1)->nullable();
            $table->integer('projects')->nullable();
            $table->string('category'); // Foreign key to categories.id
            $table->boolean('is_aiverified')->default(false);
            $table->timestamps();

            // Add user_id for regular sellers (users with is_seller=true)
            $table->uuid('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            $table->foreign('category')->references('id')->on('categories')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('professionals');
    }
};
