<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('case_studies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->string('client');
            $table->text('description');
            $table->string('image');
            $table->string('category'); // Foreign key to categories.id
            $table->timestamps();

            $table->foreign('category')->references('id')->on('categories')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('case_studies');
    }
};