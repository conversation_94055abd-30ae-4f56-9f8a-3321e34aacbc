@extends('layouts.app')

@section('title', 'Two Factor Authentication - Hermosart')

@section('content')
<div class="flex min-h-screen flex-col">
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="w-full max-w-md">
            <div class="text-center mb-8">
                <div class="mx-auto h-12 w-12 overflow-hidden rounded-full bg-[#710d17] flex items-center justify-center">
                    <span class="text-[#fcefcc] font-bold text-xl">H</span>
                </div>
                <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900">Two Factor Authentication</h2>
                <p class="mt-2 text-sm text-gray-600">
                    Please confirm access to your account by entering the authentication code provided by your authenticator application.
                </p>
            </div>
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                @if ($errors->any())
                    <div class="rounded-md bg-red-50 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    There were errors with your submission
                                </h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div x-data="{ recovery: false }">
                    <div class="mb-4 text-sm text-gray-600" x-show="! recovery">
                        Please confirm access to your account by entering the authentication code provided by your authenticator application.
                    </div>

                    <div class="mb-4 text-sm text-gray-600" x-show="recovery">
                        Please confirm access to your account by entering one of your emergency recovery codes.
                    </div>

                    <form class="space-y-6" method="POST" action="{{ route('two-factor.login') }}">
                        @csrf

                        <div x-show="! recovery">
                            <label for="code" class="block text-sm font-medium text-gray-700">
                                Code
                            </label>
                            <div class="mt-1">
                                <input id="code" name="code" type="text" inputmode="numeric" autocomplete="one-time-code" required autofocus class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                            </div>
                        </div>

                        <div x-show="recovery">
                            <label for="recovery_code" class="block text-sm font-medium text-gray-700">
                                Recovery Code
                            </label>
                            <div class="mt-1">
                                <input id="recovery_code" name="recovery_code" type="text" autocomplete="one-time-code" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <button type="button" class="text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer"
                                    x-show="! recovery"
                                    x-on:click="recovery = true">
                                Use a recovery code
                            </button>

                            <button type="button" class="text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer"
                                    x-show="recovery"
                                    x-on:click="recovery = false">
                                Use an authentication code
                            </button>

                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-[#710d17] border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                Log in
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
