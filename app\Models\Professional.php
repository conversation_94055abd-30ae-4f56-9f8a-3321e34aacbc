<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Professional extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $casts = [
        'skills' => 'array',
        'rating' => 'float',
    ];

    protected $fillable = [
        'name', 'slug', 'title', 'skills', 'category', 'is_aiverified',
        'description', 'experience', 'rating', 'projects', 'user_id'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Generate UUID if not provided
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }

            // Generate slug if not provided
            if (empty($model->slug)) {
                $model->slug = $model->generateUniqueSlug($model->name);
            }
        });

        static::updating(function ($model) {
            // Update slug if name has changed
            if ($model->isDirty('name') && !$model->isDirty('slug')) {
                $model->slug = $model->generateUniqueSlug($model->name);
            }
        });
    }

    /**
     * Generate a unique slug based on the professional's name.
     *
     * @param string $name
     * @return string
     */
    protected function generateUniqueSlug($name)
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $count = 1;

        // Make sure the slug is unique
        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $count++;
        }

        return $slug;
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category', 'id');
    }

    /**
     * Get the portfolio items for the professional.
     */
    public function portfolioItems()
    {
        return $this->hasMany(PortfolioItem::class);
    }

    /**
     * Get the services offered by the professional.
     * This assumes services have a professional_id field.
     */
    public function services()
    {
        return $this->hasMany(Service::class, 'professional_id');
    }

    /**
     * Get the products created by the professional.
     * This assumes products have a professional_id field.
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'professional_id');
    }

    /**
     * Get the user associated with this professional.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}