<?php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Validate and create a newly registered user.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {
        Validator::make($input, [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class),
            ],
            'company' => ['nullable', 'string', 'max:255'],
            'account_type' => ['required', 'string', 'in:client,professional'],
            'specialty' => ['nullable', 'string', 'max:255', 'required_if:account_type,professional'],
            'password' => $this->passwordRules(),
            'terms' => ['required', 'accepted'],
        ])->validate();

        // Create a full name from first and last name
        $name = $input['first_name'] . ' ' . $input['last_name'];

        // Determine if the user is a seller based on account type
        $isSeller = false;
        if (isset($input['is_seller'])) {
            $isSeller = (bool) $input['is_seller'];
        }

        return User::create([
            'name' => $name,
            'first_name' => $input['first_name'],
            'last_name' => $input['last_name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
            'account_type' => $input['account_type'],
            'is_seller' => $isSeller,
            'company' => $input['company'] ?? null,
            'specialty' => $input['specialty'] ?? null,
        ]);
    }
}
