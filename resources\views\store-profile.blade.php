@extends('layouts.app')

@section('title', $sellerApplication->business_name . ' - Store Profile')

@section('content')
    <div class="bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Back button -->
            <div class="mb-6">
                <a href="{{ route('stores') }}"
                    class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Stores
                </a>
            </div>

            <!-- Store profile header -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div class="px-4 py-5 sm:px-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-20 w-20 bg-gray-200 rounded-full overflow-hidden">
                            @if ($seller->store_image && Storage::exists('public/' . $seller->store_image))
                                <img src="{{ asset('storage/' . $seller->store_image) }}"
                                    alt="{{ $sellerApplication->business_name }}" class="h-full w-full object-cover">
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 p-4"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                            @endif
                        </div>
                        <div class="ml-4">
                            <h1 class="text-2xl font-bold text-gray-900">{{ $sellerApplication->business_name }}</h1>
                            <p class="text-sm text-gray-500">Store Owner: {{ $seller->name }}</p>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="mailto:{{ $seller->email }}"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Contact Store
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <div id="store-tabs" class="mx-auto mt-8 mb-10">
                <div class="bg-gray-100 rounded-lg p-1 grid grid-cols-2 gap-1">
                    <button data-tab-category="about"
                        class="tab-transition px-2 py-3 text-sm font-medium bg-white focus:outline-none text-center rounded-lg">
                        About
                    </button>
                    <button data-tab-category="products"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        Products
                    </button>
                </div>
            </div>

            <div id="store-content" class="mx-auto py-8">
                <!-- About Tab Content -->
                <div data-tab-content="about" class="tab-content">
                    <div class="bg-white shadow rounded-lg p-6 mb-8">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">About the Store</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $sellerApplication->business_description }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Store Owner</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $seller->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    {{ $sellerApplication->approved_at ? $sellerApplication->approved_at->format('F Y') : $sellerApplication->created_at->format('F Y') }}
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Products Tab Content -->
                <div data-tab-content="products" class="tab-content hidden">
                    <div class="bg-white shadow rounded-lg p-6 mb-8">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Products</h2>
                        @if (count($products) > 0)
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach ($products as $product)
                                    <div class="bg-white shadow rounded-lg overflow-hidden card-hover">
                                        <div class="h-48 bg-gray-200 relative">
                                            @if ($product->image && Storage::exists('public/' . $product->image))
                                                <img src="{{ asset('storage/' . $product->image) }}"
                                                    alt="{{ $product->name }}" class="h-full w-full object-cover">
                                            @else
                                                <div class="flex items-center justify-center h-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="p-4">
                                            <h3 class="text-lg font-semibold text-[#710d17]">{{ $product->name }}</h3>
                                            <p class="mt-1 text-sm text-gray-600">
                                                {{ Str::limit($product->description, 100) }}</p>
                                            <div class="mt-4 flex items-center justify-between">
                                                <span
                                                    class="text-base font-bold text-gray-900">${{ number_format($product->price, 2) }}</span>
                                                <a href="{{ route('products.show', $product->slug) }}"
                                                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39]">
                                                    View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No products</h3>
                                <p class="mt-1 text-sm text-gray-500">This store doesn't have any products yet.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Tab functionality
                const tabButtons = document.querySelectorAll('[data-tab-category]');
                const tabContents = document.querySelectorAll('[data-tab-content]');

                // Set initial active tab
                document.querySelector('[data-tab-category="about"]').classList.add('bg-white');
                document.querySelector('[data-tab-content="about"]').classList.remove('hidden');

                tabButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const category = button.getAttribute('data-tab-category');

                        // Update button styles
                        tabButtons.forEach(btn => {
                            btn.classList.remove('bg-white');
                        });
                        button.classList.add('bg-white');

                        // Show/hide content
                        tabContents.forEach(content => {
                            if (content.getAttribute('data-tab-content') === category) {
                                content.classList.remove('hidden');
                            } else {
                                content.classList.add('hidden');
                            }
                        });
                    });
                });
            });
        </script>
    @endpush
@endsection
