<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Professional;
use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TalentSeeder extends Seeder
{
    public function run(): void
    {

        // Categories
        $categories = [
            ['id' => 'web', 'name' => 'Web Development', 'icon' => 'code'],
            ['id' => 'data', 'name' => 'Data Solutions', 'icon' => 'database'],
            ['id' => 'ai', 'name' => 'AI Automation', 'icon' => 'cpu'],
            ['id' => 'design', 'name' => 'Design', 'icon' => 'palette'],
        ];
        foreach ($categories as $category) {
            Category::create($category);
        }

        // Professionals
        $professionals = [
            'web' => [
                [
                    'name' => '<PERSON>',
                    'title' => 'Frontend Developer',
                    'skills' => ['React', 'TypeScript', 'Tailwind CSS'],
                    'description' => 'Specializes in creating responsive, user-friendly interfaces with modern frameworks.',
                    'experience' => '8 years',
                    'rating' => 4.9,
                    'projects' => 42,
                    'category' => 'web',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'Sarah Chen',
                    'title' => 'Backend Developer',
                    'skills' => ['Node.js', 'Python', 'MongoDB'],
                    'description' => 'Expert in building scalable server-side applications and APIs.',
                    'experience' => '6 years',
                    'rating' => 4.8,
                    'projects' => 35,
                    'category' => 'web',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'Michael Brown',
                    'title' => 'Full-Stack Developer',
                    'skills' => ['JavaScript', 'AWS', 'Docker'],
                    'description' => 'Versatile developer with expertise in both frontend and backend technologies.',
                    'experience' => '10 years',
                    'rating' => 4.9,
                    'projects' => 58,
                    'category' => 'web',
                    'is_aiverified' => true,
                ],
            ],
            'data' => [
                [
                    'name' => 'John Smith',
                    'title' => 'Data Analyst',
                    'skills' => ['Python', 'Tableau', 'SQL'],
                    'description' => 'Transforms complex data into actionable business insights.',
                    'experience' => '9 years',
                    'rating' => 4.8,
                    'projects' => 45,
                    'category' => 'data',
                    'is_aiverified' => false,
                ],
                [
                    'name' => 'Emily Wilson',
                    'title' => 'Machine Learning Engineer',
                    'skills' => ['TensorFlow', 'PyTorch', 'Scikit-learn'],
                    'description' => 'Builds and deploys machine learning models for various applications.',
                    'experience' => '7 years',
                    'rating' => 4.9,
                    'projects' => 36,
                    'category' => 'data',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'Robert Lee',
                    'title' => 'Data Visualization Specialist',
                    'skills' => ['Power BI', 'D3.js', 'R'],
                    'description' => 'Creates interactive dashboards and visual representations of data.',
                    'experience' => '8 years',
                    'rating' => 4.7,
                    'projects' => 41,
                    'category' => 'data',
                    'is_aiverified' => false,
                ],
            ],
            'ai' => [
                [
                    'name' => 'Sophia Rodriguez',
                    'title' => 'AI Engineer',
                    'skills' => ['NLP', 'Computer Vision', 'Python'],
                    'description' => 'Develops AI solutions for natural language processing and image recognition.',
                    'experience' => '7 years',
                    'rating' => 4.9,
                    'projects' => 34,
                    'category' => 'ai',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'James Wilson',
                    'title' => 'Chatbot Developer',
                    'skills' => ['DialogFlow', 'RASA', 'Node.js'],
                    'description' => 'Creates conversational AI interfaces for customer service and support.',
                    'experience' => '5 years',
                    'rating' => 4.7,
                    'projects' => 28,
                    'category' => 'ai',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'Aisha Patel',
                    'title' => 'AI Solutions Architect',
                    'skills' => ['TensorFlow', 'AWS AI Services', 'GPT'],
                    'description' => 'Designs comprehensive AI systems tailored to business needs.',
                    'experience' => '9 years',
                    'rating' => 4.9,
                    'projects' => 43,
                    'category' => 'ai',
                    'is_aiverified' => true,
                ],
            ],
            'design' => [
                [
                    'name' => 'Jane Doe',
                    'title' => 'UI/UX Designer',
                    'skills' => ['Figma', 'Adobe XD', 'Sketch'],
                    'description' => 'Creates intuitive user interfaces and seamless user experiences.',
                    'experience' => '8 years',
                    'rating' => 4.9,
                    'projects' => 40,
                    'category' => 'design',
                    'is_aiverified' => false,
                ],
                [
                    'name' => 'Alex Johnson',
                    'title' => 'Graphic Designer',
                    'skills' => ['Illustrator', 'Photoshop', 'InDesign'],
                    'description' => 'Designs visual assets for digital and print media.',
                    'experience' => '7 years',
                    'rating' => 4.8,
                    'projects' => 36,
                    'category' => 'design',
                    'is_aiverified' => true,
                ],
                [
                    'name' => 'Maria Garcia',
                    'title' => 'Brand Identity Designer',
                    'skills' => ['Logo Design', 'Typography', 'Brand Strategy'],
                    'description' => 'Develops comprehensive brand identities and visual systems.',
                    'experience' => '9 years',
                    'rating' => 4.9,
                    'projects' => 44,
                    'category' => 'design',
                    'is_aiverified' => true,
                ],
            ],
        ];

        // Create random users for professionals if they don't exist
        foreach ($professionals as $category => $categoryProfessionals) {
            foreach ($categoryProfessionals as $pro) {
                // Create a user for this professional
                $email = strtolower(str_replace(' ', '.', $pro['name'])) . '@example.com';
                $user = \App\Models\User::where('email', $email)->first();

                if (!$user) {
                    $nameParts = explode(' ', $pro['name']);
                    $firstName = $nameParts[0];
                    $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

                    $user = \App\Models\User::create([
                        'name' => $pro['name'],
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'email_verified_at' => now(),
                        'password' => \Illuminate\Support\Facades\Hash::make('password'),
                        'account_type' => 'professional',
                        'is_seller' => false,
                        'specialty' => $pro['title'],
                        'remember_token' => \Illuminate\Support\Str::random(10),
                    ]);
                }

                // Add user_id to professional data
                $pro['user_id'] = $user->id;

                Professional::create($pro);
            }
        }

        // Testimonials (same as Homepage)
        Testimonial::create([
            'name' => 'Sarah',
            'role' => 'Startup Founder',
            'content' => 'Hermosart\'s AI found me the perfect web developer in hours. The verified badge gave me confidence!',
            'avatar' => 'images/testimonial-1.jpg',
        ]);
        Testimonial::create([
            'name' => 'Mark',
            'role' => 'Tech Director',
            'content' => 'The data visualization tools saved us weeks of development time. Amazing quality and support!',
            'avatar' => 'images/testimonial-2.jpg',
        ]);
        Testimonial::create([
            'name' => 'Jessica',
            'role' => 'Marketing Director',
            'content' => 'We hired an AI automation specialist through Hermosart and they exceeded our expectations. The AI matching is spot on!',
            'avatar' => 'images/testimonial-3.jpg',
        ]);
    }
}