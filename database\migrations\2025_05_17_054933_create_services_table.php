<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('category'); // Foreign key to categories.id
            $table->string('professional_id')->nullable(); // Foreign key to professionals.id
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description');
            $table->json('features');
            $table->string('icon');
            $table->timestamps();

            $table->foreign('professional_id')->references('id')->on('professionals')->onDelete('set null');
            $table->foreign('category')->references('id')->on('categories')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
