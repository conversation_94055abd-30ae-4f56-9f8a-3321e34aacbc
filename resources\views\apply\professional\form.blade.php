@extends('layouts.app')

@section('title', 'Apply to Become a Professional')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header with background image -->
            <div class="relative h-48 bg-gradient-to-r from-[#710d17] to-[#9a2c39]">
                <div class="absolute inset-0 bg-opacity-70 flex items-center justify-center">
                    <h1 class="text-3xl font-bold text-white text-center px-4">Apply to Become a Professional</h1>
                </div>
            </div>
            
            <div class="px-6 py-8 md:p-10">
                @if (session('success'))
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md" role="alert">
                        <p>{{ session('success') }}</p>
                    </div>
                @endif

                @if (session('error'))
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md" role="alert">
                        <p>{{ session('error') }}</p>
                    </div>
                @endif

                <div class="prose max-w-none mb-8">
                    <p class="text-gray-600">
                        Join our community of talented professionals and showcase your skills to potential clients. 
                        Complete the form below to apply as a professional on Hermosart. Our team will review your 
                        application and get back to you within 2-3 business days.
                    </p>
                </div>

                <form action="{{ route('apply.professional.submit') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                    @csrf

                    <!-- Professional Profile Section -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Professional Profile
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="col-span-2 md:col-span-1">
                                <label for="professional_title" class="block text-sm font-medium text-gray-700">Professional Title <span class="text-red-500">*</span></label>
                                <input type="text" name="professional_title" id="professional_title" value="{{ old('professional_title') }}" placeholder="e.g. UI/UX Designer" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                                <p class="mt-1 text-xs text-gray-500">Your professional title that will be displayed on your profile</p>
                                @error('professional_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="category" class="block text-sm font-medium text-gray-700">Category <span class="text-red-500">*</span></label>
                                <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                                    <option value="">Select a category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category') == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <label for="bio" class="block text-sm font-medium text-gray-700">Professional Bio <span class="text-red-500">*</span></label>
                                <textarea name="bio" id="bio" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>{{ old('bio') }}</textarea>
                                <p class="mt-1 text-xs text-gray-500">Describe your professional background, expertise, and what makes you unique (max 2000 characters)</p>
                                @error('bio')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <label for="skills" class="block text-sm font-medium text-gray-700">Skills <span class="text-red-500">*</span></label>
                                <div class="mt-1 flex flex-wrap gap-2" id="skills-container">
                                    <input type="text" id="skill-input" placeholder="Type a skill and press Enter" class="flex-grow rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                </div>
                                <div id="skills-tags" class="mt-2 flex flex-wrap gap-2"></div>
                                <div id="skills-hidden-inputs"></div>
                                <p class="mt-1 text-xs text-gray-500">Add your professional skills (e.g., Photoshop, JavaScript, Content Writing)</p>
                                @error('skills')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                @error('skills.*')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Experience & Education Section -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Experience & Education
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="experience_years" class="block text-sm font-medium text-gray-700">Years of Experience <span class="text-red-500">*</span></label>
                                <select name="experience_years" id="experience_years" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                                    <option value="">Select experience</option>
                                    <option value="Less than 1 year" {{ old('experience_years') == 'Less than 1 year' ? 'selected' : '' }}>Less than 1 year</option>
                                    <option value="1-2 years" {{ old('experience_years') == '1-2 years' ? 'selected' : '' }}>1-2 years</option>
                                    <option value="3-5 years" {{ old('experience_years') == '3-5 years' ? 'selected' : '' }}>3-5 years</option>
                                    <option value="5-10 years" {{ old('experience_years') == '5-10 years' ? 'selected' : '' }}>5-10 years</option>
                                    <option value="10+ years" {{ old('experience_years') == '10+ years' ? 'selected' : '' }}>10+ years</option>
                                </select>
                                @error('experience_years')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="education" class="block text-sm font-medium text-gray-700">Highest Education <span class="text-red-500">*</span></label>
                                <input type="text" name="education" id="education" value="{{ old('education') }}" placeholder="e.g. Bachelor's in Graphic Design, Self-taught" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                                @error('education')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <label for="certifications" class="block text-sm font-medium text-gray-700">Certifications & Achievements</label>
                                <textarea name="certifications" id="certifications" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">{{ old('certifications') }}</textarea>
                                <p class="mt-1 text-xs text-gray-500">List any relevant certifications, awards, or achievements</p>
                                @error('certifications')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Portfolio & Links Section -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                            Portfolio & Links
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="col-span-2 md:col-span-1">
                                <label for="portfolio_url" class="block text-sm font-medium text-gray-700">Portfolio Website</label>
                                <input type="url" name="portfolio_url" id="portfolio_url" value="{{ old('portfolio_url') }}" placeholder="https://yourportfolio.com" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('portfolio_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="linkedin_url" class="block text-sm font-medium text-gray-700">LinkedIn Profile</label>
                                <input type="url" name="linkedin_url" id="linkedin_url" value="{{ old('linkedin_url') }}" placeholder="https://linkedin.com/in/yourprofile" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('linkedin_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="github_url" class="block text-sm font-medium text-gray-700">GitHub Profile</label>
                                <input type="url" name="github_url" id="github_url" value="{{ old('github_url') }}" placeholder="https://github.com/yourusername" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('github_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="behance_url" class="block text-sm font-medium text-gray-700">Behance Profile</label>
                                <input type="url" name="behance_url" id="behance_url" value="{{ old('behance_url') }}" placeholder="https://behance.net/yourusername" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('behance_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="dribbble_url" class="block text-sm font-medium text-gray-700">Dribbble Profile</label>
                                <input type="url" name="dribbble_url" id="dribbble_url" value="{{ old('dribbble_url') }}" placeholder="https://dribbble.com/yourusername" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('dribbble_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="other_url" class="block text-sm font-medium text-gray-700">Other Website/Profile</label>
                                <input type="url" name="other_url" id="other_url" value="{{ old('other_url') }}" placeholder="https://example.com" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                @error('other_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Work Details Section -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Work Details
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="hourly_rate" class="block text-sm font-medium text-gray-700">Hourly Rate (USD)</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="hourly_rate" id="hourly_rate" value="{{ old('hourly_rate') }}" min="0" step="0.01" placeholder="0.00" class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Your preferred hourly rate (you can adjust this later)</p>
                                @error('hourly_rate')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="availability" class="block text-sm font-medium text-gray-700">Availability</label>
                                <select name="availability" id="availability" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]">
                                    <option value="">Select availability</option>
                                    <option value="Full-time" {{ old('availability') == 'Full-time' ? 'selected' : '' }}>Full-time</option>
                                    <option value="Part-time" {{ old('availability') == 'Part-time' ? 'selected' : '' }}>Part-time</option>
                                    <option value="Weekends only" {{ old('availability') == 'Weekends only' ? 'selected' : '' }}>Weekends only</option>
                                    <option value="Evenings only" {{ old('availability') == 'Evenings only' ? 'selected' : '' }}>Evenings only</option>
                                    <option value="Limited availability" {{ old('availability') == 'Limited availability' ? 'selected' : '' }}>Limited availability</option>
                                </select>
                                <p class="mt-1 text-xs text-gray-500">Your current availability for projects</p>
                                @error('availability')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Contact & Documents Section -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Contact & Documents
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number <span class="text-red-500">*</span></label>
                                <input type="text" name="phone_number" id="phone_number" value="{{ old('phone_number') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                                @error('phone_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="profile_image" class="block text-sm font-medium text-gray-700">Profile Image</label>
                                <input type="file" name="profile_image" id="profile_image" accept="image/*" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-[#710d17] file:text-white hover:file:bg-[#9a2c39]">
                                <p class="mt-1 text-xs text-gray-500">Upload a professional photo of yourself (max 2MB)</p>
                                @error('profile_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label for="resume" class="block text-sm font-medium text-gray-700">Resume/CV</label>
                                <input type="file" name="resume" id="resume" accept=".pdf,.doc,.docx" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-[#710d17] file:text-white hover:file:bg-[#9a2c39]">
                                <p class="mt-1 text-xs text-gray-500">Upload your resume (PDF, DOC, or DOCX, max 5MB)</p>
                                @error('resume')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="terms" name="terms" type="checkbox" class="h-4 w-4 text-[#710d17] focus:ring-[#710d17] border-gray-300 rounded" required>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="font-medium text-gray-700">I agree to the <a href="#" class="text-[#710d17] hover:underline">Terms and Conditions</a> and <a href="#" class="text-[#710d17] hover:underline">Professional Policy</a> <span class="text-red-500">*</span></label>
                                <p class="text-gray-500">By submitting this application, you agree to our terms for professionals and consent to our verification process.</p>
                            </div>
                        </div>
                        @error('terms')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-base font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17] transition-colors duration-200">
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const skillInput = document.getElementById('skill-input');
        const skillsContainer = document.getElementById('skills-container');
        const skillsTags = document.getElementById('skills-tags');
        const skillsHiddenInputs = document.getElementById('skills-hidden-inputs');
        const skills = [];

        // Add existing skills if any (from old input)
        @if(old('skills'))
            @foreach(old('skills') as $skill)
                addSkill("{{ $skill }}");
            @endforeach
        @endif

        skillInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                const skill = this.value.trim();
                if (skill) {
                    addSkill(skill);
                    this.value = '';
                }
            }
        });

        function addSkill(skill) {
            if (skills.includes(skill)) return;
            
            skills.push(skill);
            
            // Create tag element
            const tag = document.createElement('div');
            tag.className = 'bg-[#710d17] text-white px-3 py-1 rounded-full text-sm flex items-center';
            tag.innerHTML = `
                ${skill}
                <button type="button" class="ml-2 focus:outline-none" data-skill="${skill}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;
            
            // Add remove event
            tag.querySelector('button').addEventListener('click', function() {
                const skillToRemove = this.getAttribute('data-skill');
                removeSkill(skillToRemove);
                tag.remove();
            });
            
            skillsTags.appendChild(tag);
            
            // Add hidden input
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'skills[]';
            input.value = skill;
            input.id = `skill-${skills.length}`;
            skillsHiddenInputs.appendChild(input);
        }

        function removeSkill(skill) {
            const index = skills.indexOf(skill);
            if (index > -1) {
                skills.splice(index, 1);
                document.getElementById(`skill-${index + 1}`).remove();
            }
        }
    });
</script>
@endpush
@endsection
