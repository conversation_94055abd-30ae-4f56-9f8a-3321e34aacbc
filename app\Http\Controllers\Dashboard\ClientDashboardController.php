<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Favorite;
use App\Models\Message;
use App\Models\Order;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ClientDashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('client');
    }

    /**
     * Show the client dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();

        // Get real data from database
        $activeProjects = Project::where('client_id', $user->id)
            ->whereIn('status', ['In Progress', 'Just Started', 'Pending Approval'])
            ->count();

        $completedProjects = Project::where('client_id', $user->id)
            ->where('status', 'Completed')
            ->count();

        $unreadMessages = Message::where('recipient_id', $user->id)
            ->where('is_read', false)
            ->count();

        $savedProfessionals = Favorite::where('user_id', $user->id)
            ->count();

        $stats = [
            'active_projects' => $activeProjects,
            'completed_projects' => $completedProjects,
            'unread_messages' => $unreadMessages,
            'saved_professionals' => $savedProfessionals
        ];

        // Get recent projects with professional information
        $recentProjects = Project::where('client_id', $user->id)
            ->with('professional')
            ->orderBy('updated_at', 'desc')
            ->take(3)
            ->get();

        // Get recent messages with sender information
        $recentMessages = Message::where('recipient_id', $user->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get();

        return view('dashboard.client.index', compact('user', 'stats', 'recentProjects', 'recentMessages'));
    }

    /**
     * Show the client projects page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function projects()
    {
        $user = Auth::user();

        $projects = Project::where('client_id', $user->id)
            ->with('professional')
            ->orderBy('updated_at', 'desc')
            ->get();

        // Get professionals for the new project form
        $professionals = User::where('account_type', 'professional')->get();

        return view('dashboard.client.projects', compact('projects', 'professionals'));
    }

    /**
     * Show a specific project.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showProject($id)
    {
        $user = Auth::user();

        $project = Project::where('id', $id)
            ->where('client_id', $user->id)
            ->with('professional')
            ->firstOrFail();

        return view('dashboard.client.project-details', compact('project'));
    }

    /**
     * Show the form for creating a new project.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createProject()
    {
        // Get professionals for the form
        $professionals = User::where('account_type', 'professional')->get();

        return view('dashboard.client.create-project', compact('professionals'));
    }

    /**
     * Store a new project.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeProject(Request $request)
    {
        $user = Auth::user();

        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'professional_id' => 'required|exists:users,id',
                'due_date' => 'required|date|after_or_equal:today',
                'budget' => 'required|numeric|min:0',
            ]);

            // Create a new project with UUID
            $project = new Project();
            $project->id = (string) Str::uuid(); // Ensure UUID is set
            $project->client_id = $user->id;
            $project->professional_id = $validated['professional_id'];
            $project->title = $validated['title'];
            $project->description = $validated['description'];
            $project->status = 'Pending Approval';
            $project->start_date = now();
            $project->due_date = $validated['due_date'];
            $project->progress = 0;
            $project->budget = $validated['budget'];

            // Save to database
            $saved = $project->save();

            if (!$saved) {
                return redirect()->route('dashboard.client.projects.create')
                    ->with('error', 'Failed to save project. Please try again.')
                    ->withInput();
            }

            return redirect()->route('dashboard.client.projects')
                ->with('success', 'Project created successfully');

        } catch (\Exception $e) {
            Log::error('Error creating project: ' . $e->getMessage());

            return redirect()->route('dashboard.client.projects.create')
                ->with('error', 'An error occurred while creating the project: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the client messages page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function messages()
    {
        $user = Auth::user();

        // Get all messages (both sent and received)
        $allMessages = Message::where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->with(['sender', 'recipient', 'project'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Group messages by conversation (unique combination of participants and project)
        $conversations = collect();
        $processedConversations = [];
        $latestMessageByConversation = [];

        foreach ($allMessages as $message) {
            // Determine the other participant
            $otherParticipantId = ($message->sender_id == $user->id)
                ? $message->recipient_id
                : $message->sender_id;

            // Create a unique key for this conversation
            $projectId = $message->project_id ?? 'no_project';
            $conversationKey = $otherParticipantId . '_' . $projectId;

            // Track the latest message for each conversation
            if (!isset($latestMessageByConversation[$conversationKey]) ||
                $message->created_at->gt($latestMessageByConversation[$conversationKey]->created_at)) {
                $latestMessageByConversation[$conversationKey] = $message;
            }
        }

        // Add all latest messages to the conversations collection
        foreach ($latestMessageByConversation as $message) {
            $conversations->push($message);
        }

        // Sort conversations by the most recent message first
        $conversations = $conversations->sortByDesc(function($message) {
            return $message->created_at;
        });

        // For backward compatibility with the template
        $messages = $conversations;

        return view('dashboard.client.messages', compact('messages'));
    }

    /**
     * Show the form for creating a new message.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createMessage()
    {
        // Get professionals for the recipient dropdown
        $professionals = User::where('account_type', 'professional')->get();

        // Get projects for the related project dropdown
        $user = Auth::user();
        $projects = Project::where('client_id', $user->id)->get();

        return view('dashboard.client.create-message', compact('professionals', 'projects'));
    }

    /**
     * Store a new message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function storeMessage(Request $request)
    {
        $user = Auth::user();

        try {
            $validated = $request->validate([
                'recipient_id' => 'required|exists:users,id',
                'subject' => 'required|string|max:255',
                'content' => 'required|string',
                'project_id' => 'nullable|exists:projects,id',
                'message_id' => 'nullable|exists:messages,id',
            ]);

            // Create a new message with UUID
            $message = new Message();
            $message->id = (string) Str::uuid();
            $message->sender_id = $user->id;
            $message->recipient_id = $validated['recipient_id'];
            $message->subject = $validated['subject'];
            $message->content = $validated['content'];
            $message->is_read = false;

            if (!empty($validated['project_id'])) {
                $message->project_id = $validated['project_id'];
            }

            // Save to database
            $saved = $message->save();

            if (!$saved) {
                if ($request->ajax()) {
                    return response()->json(['success' => false, 'message' => 'Failed to send message']);
                }

                return redirect()->route('dashboard.client.messages.create')
                    ->with('error', 'Failed to send message. Please try again.')
                    ->withInput();
            }

            // If this is an AJAX request (reply from message details page)
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Message sent successfully',
                    'messageId' => $message->id
                ]);
            }

            // If this is a regular form submission (from new message page)
            return redirect()->route('dashboard.client.messages')
                ->with('success', 'Message sent successfully');

        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
            }

            return redirect()->route('dashboard.client.messages.create')
                ->with('error', 'An error occurred while sending the message: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Check for new messages in a conversation.
     *
     * @param  string  $messageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkNewMessages($messageId)
    {
        $user = Auth::user();

        try {
            // Get the original message to identify the conversation
            $originalMessage = Message::findOrFail($messageId);

            // Determine the other participant
            $otherParticipantId = ($originalMessage->sender_id == $user->id)
                ? $originalMessage->recipient_id
                : $originalMessage->sender_id;

            // Check if there are any new messages from the other participant
            // that are newer than the last time the user loaded the page
            $hasNewMessages = Message::where('sender_id', $otherParticipantId)
                ->where('recipient_id', $user->id)
                ->where('created_at', '>', now()->subMinutes(1)) // Only check for messages in the last minute
                ->where('is_read', false)
                ->when($originalMessage->project_id, function($query) use ($originalMessage) {
                    return $query->where('project_id', $originalMessage->project_id);
                })
                ->exists();

            return response()->json(['hasNewMessages' => $hasNewMessages]);

        } catch (\Exception $e) {
            Log::error('Error checking for new messages: ' . $e->getMessage());
            return response()->json(['hasNewMessages' => false, 'error' => $e->getMessage()]);
        }
    }

    /**
     * Show a specific message.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showMessage($id)
    {
        $user = Auth::user();

        $message = Message::where('id', $id)
            ->where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->with(['sender', 'recipient', 'project'])
            ->firstOrFail();

        // Mark as read if user is the recipient
        if ($message->recipient_id === $user->id && !$message->is_read) {
            $message->is_read = true;
            $message->save();
        }

        // Get related messages (conversation)
        // Fetch all messages between the same participants and with the same project_id if applicable
        $otherParticipantId = ($message->sender_id == $user->id) ? $message->recipient_id : $message->sender_id;

        $relatedMessages = Message::where('id', '!=', $message->id)
            ->where(function($query) use ($user, $otherParticipantId) {
                $query->where(function($q) use ($user, $otherParticipantId) {
                    $q->where('sender_id', $user->id)
                      ->where('recipient_id', $otherParticipantId);
                })->orWhere(function($q) use ($user, $otherParticipantId) {
                    $q->where('sender_id', $otherParticipantId)
                      ->where('recipient_id', $user->id);
                });
            })
            ->when($message->project_id, function($query) use ($message) {
                return $query->where('project_id', $message->project_id);
            })
            ->with(['sender', 'recipient'])
            ->orderBy('created_at', 'asc')
            ->get();

        return view('dashboard.client.message-details', compact('message', 'relatedMessages'));
    }

    /**
     * Show the client favorites page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function favorites()
    {
        $user = Auth::user();

        $favorites = Favorite::where('user_id', $user->id)
            ->with('professional')
            ->paginate(9); // Paginate for better performance with many favorites

        return view('dashboard.client.favorites', compact('favorites'));
    }

    /**
     * Toggle a professional as favorite.
     *
     * @param  string  $professional_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleFavorite($professional_id)
    {
        $user = Auth::user();

        try {
            // Check if the professional exists
            $professional = User::where('id', $professional_id)
                ->where('account_type', 'professional')
                ->firstOrFail();

            // Check if already favorited
            $favorite = Favorite::where('user_id', $user->id)
                ->where('professional_id', $professional_id)
                ->first();

            if ($favorite) {
                // If already a favorite, remove it
                $favorite->delete();
                $message = 'Professional removed from favorites';
            } else {
                // If not a favorite, add it
                $favorite = new Favorite();
                $favorite->id = (string) Str::uuid();
                $favorite->user_id = $user->id;
                $favorite->professional_id = $professional_id;
                $favorite->save();
                $message = 'Professional added to favorites';
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            Log::error('Error toggling favorite: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'An error occurred while updating favorites: ' . $e->getMessage());
        }
    }

    /**
     * Show the client orders page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function orders()
    {
        $user = Auth::user();

        $orders = Order::where('user_id', $user->id)
            ->with(['product', 'service'])
            ->orderBy('created_at', 'desc')
            ->paginate(10); // Paginate for better performance with many orders

        return view('dashboard.client.orders', compact('orders'));
    }

    /**
     * Show a specific order.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showOrder($id)
    {
        $user = Auth::user();

        $order = Order::where('id', $id)
            ->where('user_id', $user->id)
            ->with(['product', 'service'])
            ->firstOrFail();

        return view('dashboard.client.order-details', compact('order'));
    }

    /**
     * Download order files.
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function downloadOrder($id)
    {
        $user = Auth::user();

        // Verify the order exists and belongs to the user
        Order::where('id', $id)
            ->where('user_id', $user->id)
            ->where('status', 'Completed')
            ->firstOrFail();

        // For demonstration purposes, we'll just redirect back with a message
        // In a real application, you would return the file download
        return redirect()->back()->with('info', 'Download functionality will be implemented in the future.');
    }

    /**
     * Show the client profile page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = Auth::user();

        return view('dashboard.client.profile', compact('user'));
    }

    /**
     * Show the client settings page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function settings()
    {
        $user = Auth::user();

        return view('dashboard.client.settings', compact('user'));
    }

    /**
     * Update the client profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'company' => 'nullable|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        // Update user data
        User::where('id', $user->id)->update([
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'name' => $validated['first_name'] . ' ' . $validated['last_name'],
            'company' => $validated['company'],
            'email' => $validated['email']
        ]);

        return redirect()->route('client.profile')->with('success', 'Profile updated successfully');
    }
}
