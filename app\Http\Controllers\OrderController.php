<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\Earning;

class OrderController extends Controller
{
    /**
     * Complete an order (client action)
     */
    public function complete(Request $request, $orderId)
    {
        try {
            $user = Auth::user();
            $order = Order::where('id', $orderId)
                ->where('user_id', $user->id)
                ->with(['service', 'product'])
                ->firstOrFail();

            // Check if order can be completed
            if (!$order->canBeCompleted()) {
                return redirect()->back()->with('error', 'This order cannot be completed at this time.');
            }

            DB::transaction(function () use ($order) {
                // Update order status
                $order->update([
                    'status' => 'Completed',
                    'completed_at' => now()
                ]);

                // Update earnings status to release payment
                $earnings = Earning::where('order_id', $order->id)->get();
                foreach ($earnings as $earning) {
                    $earning->update(['status' => 'Paid']);
                }
            });

            return redirect()->back()->with('success', 'Order completed successfully! Payment has been released to the professional.');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to complete order. Please try again.');
        }
    }

    /**
     * Cancel an order (client action)
     */
    public function cancel(Request $request, $orderId)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $user = Auth::user();
            $order = Order::where('id', $orderId)
                ->where('user_id', $user->id)
                ->with(['service', 'product'])
                ->firstOrFail();

            // Check if order can be cancelled
            if (!$order->canBeCancelled()) {
                return redirect()->back()->with('error', 'This order cannot be cancelled at this time.');
            }

            DB::transaction(function () use ($order, $request) {
                // Update order status
                $order->update([
                    'status' => 'Cancelled',
                    'cancellation_reason' => $request->reason,
                    'completed_at' => now()
                ]);

                // Update earnings status (keep as pending for review)
                $earnings = Earning::where('order_id', $order->id)->get();
                foreach ($earnings as $earning) {
                    $earning->update([
                        'status' => 'Cancelled',
                        'description' => $earning->description . ' (Order cancelled by client)'
                    ]);
                }
            });

            return redirect()->back()->with('success', 'Order cancelled successfully. Our team will review the cancellation.');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to cancel order. Please try again.');
        }
    }
}
