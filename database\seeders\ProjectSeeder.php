<?php

namespace Database\Seeders;

use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProjectSeeder extends Seeder
{
    public function run(): void
    {
        // Get client and professional users
        $client = User::where('email', '<EMAIL>')->first();
        $professional = User::where('email', '<EMAIL>')->first();
        $proSeller = User::where('email', '<EMAIL>')->first();
        
        if (!$client || !$professional || !$proSeller) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }
        
        // Create projects
        Project::create([
            'client_id' => $client->id,
            'professional_id' => $professional->id,
            'title' => 'E-commerce Website Redesign',
            'description' => 'Complete redesign of our e-commerce platform to improve user experience and conversion rates.',
            'status' => 'In Progress',
            'start_date' => Carbon::now()->subDays(15),
            'due_date' => Carbon::now()->addDays(30),
            'progress' => 45,
            'budget' => 5000.00,
        ]);
        
        Project::create([
            'client_id' => $client->id,
            'professional_id' => $proSeller->id,
            'title' => 'Mobile App Development',
            'description' => 'Developing a native mobile application for iOS and Android platforms.',
            'status' => 'Just Started',
            'start_date' => Carbon::now()->subDays(5),
            'due_date' => Carbon::now()->addDays(60),
            'progress' => 10,
            'budget' => 8500.00,
        ]);
        
        Project::create([
            'client_id' => $client->id,
            'professional_id' => $professional->id,
            'title' => 'Brand Identity Design',
            'description' => 'Creating a complete brand identity including logo, color palette, typography, and brand guidelines.',
            'status' => 'Pending Approval',
            'start_date' => Carbon::now()->subDays(25),
            'due_date' => Carbon::now()->subDays(2),
            'progress' => 95,
            'budget' => 3200.00,
        ]);
        
        Project::create([
            'client_id' => $client->id,
            'professional_id' => $proSeller->id,
            'title' => 'SEO Optimization',
            'description' => 'Comprehensive SEO audit and optimization to improve search engine rankings.',
            'status' => 'Completed',
            'start_date' => Carbon::now()->subDays(45),
            'due_date' => Carbon::now()->subDays(10),
            'progress' => 100,
            'budget' => 1800.00,
        ]);
    }
}
