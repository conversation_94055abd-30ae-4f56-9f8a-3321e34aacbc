<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProfessionalApplication;
use App\Models\User;
use App\Models\Professional;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProfessionalApplicationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of the professional applications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $applications = ProfessionalApplication::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.professional-applications.index', compact('applications'));
    }

    /**
     * Display the specified professional application.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $application = ProfessionalApplication::with('user')->findOrFail($id);

        return view('admin.professional-applications.show', compact('application'));
    }

    /**
     * Approve the specified professional application.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve($id)
    {
        $application = ProfessionalApplication::findOrFail($id);

        // Only pending applications can be approved
        if ($application->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending applications can be approved.');
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Update the application status
            $application->status = 'approved';
            $application->approved_at = now();
            $application->save();

            // Update the user's account type
            $user = User::findOrFail($application->user_id);
            $user->account_type = 'professional';
            $user->save();

            // Create a professional record
            $professional = new Professional([
                'user_id' => $user->id,
                'title' => $application->professional_title,
                'category_id' => $application->category,
                'bio' => $application->bio,
                'skills' => $application->skills,
                'experience' => $application->experience_years,
                'education' => $application->education,
                'certifications' => $application->certifications,
                'hourly_rate' => $application->hourly_rate,
                'availability' => $application->availability,
                'portfolio_url' => $application->portfolio_url,
                'linkedin_url' => $application->linkedin_url,
                'github_url' => $application->github_url,
                'behance_url' => $application->behance_url,
                'dribbble_url' => $application->dribbble_url,
                'other_url' => $application->other_url,
                'phone' => $application->phone_number,
                'is_verified' => false,
                'is_featured' => false,
                'slug' => Str::slug($application->professional_title . '-' . Str::random(8))
            ]);

            $professional->save();

            DB::commit();

            return redirect()->route('admin.professional-applications.index')
                ->with('success', 'Professional application approved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'An error occurred while approving the application: ' . $e->getMessage());
        }
    }

    /**
     * Reject the specified professional application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, $id)
    {
        $application = ProfessionalApplication::findOrFail($id);

        // Only pending applications can be rejected
        if ($application->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending applications can be rejected.');
        }

        // Validate the rejection reason
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        // Update the application status
        $application->status = 'rejected';
        $application->rejection_reason = $request->rejection_reason;
        $application->save();

        return redirect()->route('admin.professional-applications.index')
            ->with('success', 'Professional application rejected successfully.');
    }

    /**
     * Download the resume document.
     *
     * @param  string  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadResume($id)
    {
        $application = ProfessionalApplication::findOrFail($id);

        if ($application->resume) {
            return Storage::disk('public')->download($application->resume);
        }

        return redirect()->back()->with('error', 'Resume not found.');
    }
}
