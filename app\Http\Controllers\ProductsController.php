<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProductsController extends Controller
{
    /**
     * Show the products page.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $productCategories = Category::all()->toArray();

        // Get all products
        $allProducts = Product::all();

        // Group products by category
        $products = $allProducts
            ->groupBy('category')
            ->map(function ($group) {
                return $group->toArray();
            })
            ->toArray();

        // Featured products (specific UUIDs or by criteria like high rating/sales)
        $featuredProductIds = [
            '550e8400-e29b-41d4-a716-************', // Admin Dashboard UI Kit
            '550e8400-e29b-41d4-a716-************', // Chatbot Starter Kit
            '550e8400-e29b-41d4-a716-************', // Brand Identity Package
        ];
        $featuredProducts = Product::whereIn('id', $featuredProductIds)->get()->toArray();

        return view('products', compact('productCategories', 'products', 'featuredProducts'));
    }

    /**
     * Show a specific product's details.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // Find the product by ID or slug
        $product = Product::where('id', $slug)
            ->orWhere('slug', $slug)
            ->firstOrFail();

        // Get related products from the same category
        $relatedProducts = Product::where('category', $product->category)
            ->where('id', '!=', $product->id)
            ->take(4)
            ->get();

        // Get seller information
        $seller = null;
        $sellerApplication = null;
        $storeSlug = null;

        if ($product->user_id) {
            $seller = User::find($product->user_id);
            if ($seller && $seller->is_seller) {
                $sellerApplication = SellerApplication::where('user_id', $seller->id)
                    ->where('status', 'approved')
                    ->first();

                if ($sellerApplication) {
                    $storeSlug = Str::slug($sellerApplication->business_name);
                }
            }
        }

        return view('product-detail', compact('product', 'relatedProducts', 'seller', 'sellerApplication', 'storeSlug'));
    }
}