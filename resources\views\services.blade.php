@extends('layouts.app')

@section('title', 'Services - Hermosart')

@section('content')
    <!-- Hero Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h1 class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc]">
                        Our Services
                    </h1>
                    <p class="max-w-[600px] text-zinc-200 md:text-xl">
                        Comprehensive tech solutions powered by AI-verified talent
                    </p>
                </div>
                <div class="w-full max-w-sm space-y-2">
                    <a href="#contact"
                        class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Get Started
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Categories Section -->
    <section id="services-categories" class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-8">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        What We Offer
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Our comprehensive range of services is designed to meet all your tech needs, delivered by
                        AI-verified professionals.
                    </p>
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="mx-auto max-w-6xl mb-8">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Search input -->
                        <div class="flex-grow">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <input type="text" id="service-search"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full pl-10 p-2.5"
                                    placeholder="Search by service name or description...">
                            </div>
                        </div>

                        <!-- Apply filters button -->
                        <button id="apply-filters"
                            class="bg-[#710d17] hover:bg-[#9a2c39] text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                            Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabs for service categories -->
            <div class="mx-auto max-w-6xl">
                <div class="mb-6 border-b border-gray-200">
                    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="serviceTabs" role="tablist">
                        <li class="mr-2" role="presentation">
                            <button class="inline-block p-4 border-b-2 border-[#710d17] rounded-t-lg text-[#710d17]"
                                id="web-tab" data-tab="web" type="button" role="tab" aria-selected="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="inline-block mr-2 h-4 w-4">
                                    <polyline points="16 18 22 12 16 6"></polyline>
                                    <polyline points="8 6 2 12 8 18"></polyline>
                                </svg>
                                Web Development
                            </button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button
                                class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="data-tab" data-tab="data" type="button" role="tab" aria-selected="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="inline-block mr-2 h-4 w-4">
                                    <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                                    <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                    <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                </svg>
                                Data Solutions
                            </button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button
                                class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="ai-tab" data-tab="ai" type="button" role="tab" aria-selected="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="inline-block mr-2 h-4 w-4">
                                    <rect x="4" y="4" width="16" height="16" rx="2" ry="2">
                                    </rect>
                                    <rect x="9" y="9" width="6" height="6"></rect>
                                    <line x1="9" y1="1" x2="9" y2="4"></line>
                                    <line x1="15" y1="1" x2="15" y2="4"></line>
                                    <line x1="9" y1="20" x2="9" y2="23"></line>
                                    <line x1="15" y1="20" x2="15" y2="23"></line>
                                    <line x1="20" y1="9" x2="23" y2="9"></line>
                                    <line x1="20" y1="14" x2="23" y2="14"></line>
                                    <line x1="1" y1="9" x2="4" y2="9"></line>
                                    <line x1="1" y1="14" x2="4" y2="14"></line>
                                </svg>
                                AI Automation
                            </button>
                        </li>
                        <li role="presentation">
                            <button
                                class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="design-tab" data-tab="design" type="button" role="tab" aria-selected="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="inline-block mr-2 h-4 w-4">
                                    <circle cx="13.5" cy="6.5" r=".5"></circle>
                                    <circle cx="17.5" cy="10.5" r=".5"></circle>
                                    <circle cx="8.5" cy="7.5" r=".5"></circle>
                                    <circle cx="6.5" cy="12.5" r=".5"></circle>
                                    <path
                                        d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z">
                                    </path>
                                </svg>
                                Design
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Tab content -->
                @foreach ($services as $category => $categoryServices)
                    <div id="{{ $category }}-content"
                        class="tab-content {{ $category === 'web' ? 'block' : 'hidden' }}">
                        <div class="grid gap-6 md:grid-cols-2">
                            @foreach ($categoryServices as $service)
                                <div class="overflow-hidden rounded-lg border bg-white shadow">
                                    <div class="p-6">
                                        <div class="flex items-center gap-4">
                                            <div class="p-2 bg-[#fcefcc] rounded-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="#710d17" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                                                    @if ($service['icon'] === 'code')
                                                        <polyline points="16 18 22 12 16 6"></polyline>
                                                        <polyline points="8 6 2 12 8 18"></polyline>
                                                    @elseif($service['icon'] === 'database')
                                                        <ellipse cx="12" cy="5" rx="9"
                                                            ry="3"></ellipse>
                                                        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                                        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                                    @elseif($service['icon'] === 'cpu')
                                                        <rect x="4" y="4" width="16" height="16" rx="2"
                                                            ry="2"></rect>
                                                        <rect x="9" y="9" width="6" height="6"></rect>
                                                        <line x1="9" y1="1" x2="9"
                                                            y2="4"></line>
                                                        <line x1="15" y1="1" x2="15"
                                                            y2="4"></line>
                                                        <line x1="9" y1="20" x2="9"
                                                            y2="23"></line>
                                                        <line x1="15" y1="20" x2="15"
                                                            y2="23"></line>
                                                        <line x1="20" y1="9" x2="23"
                                                            y2="9"></line>
                                                        <line x1="20" y1="14" x2="23"
                                                            y2="14"></line>
                                                        <line x1="1" y1="9" x2="4"
                                                            y2="9"></line>
                                                        <line x1="1" y1="14" x2="4"
                                                            y2="14"></line>
                                                    @elseif($service['icon'] === 'palette')
                                                        <circle cx="13.5" cy="6.5" r=".5"></circle>
                                                        <circle cx="17.5" cy="10.5" r=".5"></circle>
                                                        <circle cx="8.5" cy="7.5" r=".5"></circle>
                                                        <circle cx="6.5" cy="12.5" r=".5"></circle>
                                                        <path
                                                            d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z">
                                                        </path>
                                                    @endif
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <h3 class="text-xl font-bold">{{ $service['title'] }}</h3>
                                                <p class="mt-1 text-zinc-600">{{ $service['description'] }}</p>
                                                @if(isset($service['price']) && $service['price'] > 0)
                                                    <p class="mt-2 text-lg font-bold text-[#710d17]">${{ number_format($service['price'], 2) }}</p>
                                                @endif

                                                <!-- Professional Info -->
                                                @if(isset($service['professional']) && $service['professional'])
                                                    <div class="mt-3 flex items-center gap-2">
                                                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                            </svg>
                                                        </div>
                                                        <div class="flex-1 min-w-0">
                                                            <p class="text-sm font-medium text-gray-900 truncate">{{ $service['professional']['name'] }}</p>
                                                            <div class="flex items-center gap-2">
                                                                <p class="text-xs text-gray-500">{{ $service['professional']['title'] }}</p>
                                                                @if($service['professional']['is_aiverified'])
                                                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                        </svg>
                                                                        AI Verified
                                                                    </span>
                                                                @endif
                                                            </div>
                                                            @if(isset($service['professional']['rating']) && $service['professional']['rating'])
                                                                <div class="flex items-center mt-1">
                                                                    <div class="flex items-center">
                                                                        @for($i = 1; $i <= 5; $i++)
                                                                            <svg class="w-3 h-3 {{ $i <= $service['professional']['rating'] ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                            </svg>
                                                                        @endfor
                                                                        <span class="text-xs text-gray-500 ml-1">({{ number_format($service['professional']['rating'], 1) }})</span>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="mt-6">
                                            <h4 class="text-sm font-semibold text-zinc-900 mb-2">Key Features:</h4>
                                            <ul class="space-y-2">
                                                @foreach ($service['features'] as $feature)
                                                    <li class="flex items-center gap-2">
                                                        <div class="h-1.5 w-1.5 rounded-full bg-[#710d17]"></div>
                                                        <span class="text-sm">{{ $feature }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                            <div class="mt-6 space-y-3">
                                                @if(isset($service['price']) && $service['price'] > 0)
                                                    <div class="flex gap-2">
                                                        <a href="{{ route('services.show', $service['slug'] ?? $service['id']) }}"
                                                            class="flex-1 inline-flex items-center justify-center rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#710d17] hover:text-white">
                                                            Learn More
                                                        </a>
                                                        @auth
                                                            <form action="{{ route('payment.service.initiate', $service['id']) }}" method="POST" class="flex-1">
                                                                @csrf
                                                                <button type="submit"
                                                                    class="w-full inline-flex items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                                                    Buy Now
                                                                </button>
                                                            </form>
                                                        @else
                                                            <a href="{{ route('login') }}"
                                                                class="flex-1 inline-flex items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                                                Buy Now
                                                            </a>
                                                        @endauth
                                                    </div>
                                                @else
                                                    <a href="{{ route('services.show', $service['slug'] ?? $service['id']) }}"
                                                        class="inline-flex w-full items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                                        Learn More
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Case Studies Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Case Studies
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        See how our services have helped businesses achieve their goals and drive success.
                    </p>
                </div>
            </div>

            <div class="grid gap-8 md:grid-cols-3">
                @foreach ($caseStudies as $caseStudy)
                    <div class="overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                        <div class="grid aspect-video overflow-hidden justify-center items-center">
                            @if ($caseStudy['image'] && Storage::exists('public/' . $caseStudy['image']))
                                <img src="{{ asset($caseStudy['image']) }}" alt="{{ $caseStudy['title'] }}"
                                    class="h-full w-full object-cover transition-transform hover:scale-105">
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            @endif
                        </div>
                        <div class="p-6">
                            <div class="mb-2">
                                <span
                                    class="inline-flex items-center rounded-md bg-[#fcefcc]/30 px-2 py-1 text-xs font-medium text-[#710d17]">
                                    {{ ucfirst($caseStudy['category']) }}
                                </span>
                            </div>
                            <h3 class="text-xl font-bold">{{ $caseStudy['title'] }}</h3>
                            <p class="mt-1 text-sm text-zinc-500">Client: {{ $caseStudy['client'] }}</p>
                            <p class="mt-3 text-zinc-600">{{ $caseStudy['description'] }}</p>
                            <div class="mt-4">
                                <a href="#" class="text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                                    Read full case study →
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Our Process
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        We follow a structured approach to ensure your project is delivered successfully.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-4xl">
                <div class="grid gap-8 md:grid-cols-4">
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <span class="text-xl font-bold text-[#710d17]">1</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">Discovery</h3>
                        <p class="text-sm text-zinc-600">We understand your requirements and project goals.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <span class="text-xl font-bold text-[#710d17]">2</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">Planning</h3>
                        <p class="text-sm text-zinc-600">We create a detailed roadmap and match you with the right talent.
                        </p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <span class="text-xl font-bold text-[#710d17]">3</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">Execution</h3>
                        <p class="text-sm text-zinc-600">Our AI-verified professionals deliver high-quality work.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <span class="text-xl font-bold text-[#710d17]">4</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">Support</h3>
                        <p class="text-sm text-zinc-600">We provide ongoing support and maintenance as needed.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="w-full py-12 md:py-24 lg:py-32 bg-[#fcefcc]/30">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Ready to Get Started?
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Contact us today to discuss your project needs and how our AI-verified talent can help you succeed.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-3xl">
                <div class="overflow-hidden rounded-lg border bg-white shadow">
                    <div class="p-6">
                        <form class="space-y-4">
                            <div class="grid gap-4 md:grid-cols-2">
                                <div>
                                    <label for="name"
                                        class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input type="text" id="name" name="name"
                                        class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]"
                                        required>
                                </div>
                                <div>
                                    <label for="email"
                                        class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="email" name="email"
                                        class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]"
                                        required>
                                </div>
                            </div>
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">Company
                                    (optional)</label>
                                <input type="text" id="company" name="company"
                                    class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]">
                            </div>
                            <div>
                                <label for="service" class="block text-sm font-medium text-gray-700 mb-1">Service
                                    Interested In</label>
                                <select id="service" name="service"
                                    class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]"
                                    required>
                                    <option value="">Select a service</option>
                                    <option value="web-development">Web Development</option>
                                    <option value="data-solutions">Data Solutions</option>
                                    <option value="ai-automation">AI Automation</option>
                                    <option value="design">Design Services</option>
                                </select>
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                                <textarea id="message" name="message" rows="4"
                                    class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]"
                                    required></textarea>
                            </div>
                            <div>
                                <button type="submit"
                                    class="inline-flex w-full items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                    Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Frequently Asked Questions
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Find answers to common questions about our services.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-3xl">
                <div class="space-y-4">
                    <div class="rounded-lg border border-gray-200">
                        <button
                            class="flex w-full items-center justify-between rounded-lg px-4 py-3 text-left text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus-visible:ring focus-visible:ring-[#710d17]"
                            id="faq-1-button" aria-expanded="false" aria-controls="faq-1-panel">
                            <span>How does your AI talent verification work?</span>
                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="hidden px-4 pb-3 pt-0" id="faq-1-panel" aria-labelledby="faq-1-button">
                            <p class="text-sm text-gray-700">Our AI verification process evaluates professionals based on
                                their skills, experience, and past work. We use a combination of technical assessments,
                                portfolio reviews, and reference checks to ensure that only the most qualified professionals
                                are matched with your projects.</p>
                        </div>
                    </div>
                    <div class="rounded-lg border border-gray-200">
                        <button
                            class="flex w-full items-center justify-between rounded-lg px-4 py-3 text-left text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus-visible:ring focus-visible:ring-[#710d17]"
                            id="faq-2-button" aria-expanded="false" aria-controls="faq-2-panel">
                            <span>What is your pricing model?</span>
                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="hidden px-4 pb-3 pt-0" id="faq-2-panel" aria-labelledby="faq-2-button">
                            <p class="text-sm text-gray-700">We offer flexible pricing models including fixed-price
                                projects, hourly rates, and retainer agreements. The specific pricing depends on the scope
                                of work, timeline, and expertise required. We provide detailed quotes after understanding
                                your project requirements.</p>
                        </div>
                    </div>
                    <div class="rounded-lg border border-gray-200">
                        <button
                            class="flex w-full items-center justify-between rounded-lg px-4 py-3 text-left text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus-visible:ring focus-visible:ring-[#710d17]"
                            id="faq-3-button" aria-expanded="false" aria-controls="faq-3-panel">
                            <span>How long does a typical project take?</span>
                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="hidden px-4 pb-3 pt-0" id="faq-3-panel" aria-labelledby="faq-3-button">
                            <p class="text-sm text-gray-700">Project timelines vary based on complexity and scope. A simple
                                website might take 2-4 weeks, while a complex web application could take 2-6 months. Data
                                and AI projects also vary widely. We provide detailed timelines during the planning phase
                                after understanding your specific requirements.</p>
                        </div>
                    </div>
                    <div class="rounded-lg border border-gray-200">
                        <button
                            class="flex w-full items-center justify-between rounded-lg px-4 py-3 text-left text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus-visible:ring focus-visible:ring-[#710d17]"
                            id="faq-4-button" aria-expanded="false" aria-controls="faq-4-panel">
                            <span>Do you offer ongoing support after project completion?</span>
                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="hidden px-4 pb-3 pt-0" id="faq-4-panel" aria-labelledby="faq-4-button">
                            <p class="text-sm text-gray-700">Yes, we offer various support and maintenance packages to
                                ensure your project continues to run smoothly after launch. These can include regular
                                updates, security patches, performance monitoring, and technical support. We can customize a
                                support plan based on your specific needs.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabs = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('border-[#710d17]', 'text-[#710d17]');
                        t.classList.add('border-transparent', 'hover:text-gray-600',
                            'hover:border-gray-300');
                        t.setAttribute('aria-selected', 'false');
                    });

                    // Add active class to clicked tab
                    tab.classList.add('border-[#710d17]', 'text-[#710d17]');
                    tab.classList.remove('border-transparent', 'hover:text-gray-600',
                        'hover:border-gray-300');
                    tab.setAttribute('aria-selected', 'true');

                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Show the selected tab content
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-content`).classList.remove('hidden');

                    // Reset search when changing tabs
                    document.getElementById('service-search').value = '';

                    // Show all services in the current tab
                    showAllServices();
                });
            });

            // Search functionality
            const searchInput = document.getElementById('service-search');
            const applyFiltersBtn = document.getElementById('apply-filters');

            // Apply search when button is clicked
            applyFiltersBtn.addEventListener('click', searchServices);

            // Apply search when Enter key is pressed in search input
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    searchServices();
                }
            });

            function searchServices() {
                const searchTerm = searchInput.value.toLowerCase();

                // Get all service cards in the currently visible tab content
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const serviceCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow');

                serviceCards.forEach(card => {
                    let shouldShow = true;

                    // Filter by search term
                    if (searchTerm) {
                        const title = card.querySelector('h3').textContent.toLowerCase();
                        const description = card.querySelector('p.mt-1.text-zinc-600').textContent
                            .toLowerCase();
                        const features = Array.from(card.querySelectorAll('li span')).map(span => span
                            .textContent.toLowerCase());

                        const matchesSearch =
                            title.includes(searchTerm) ||
                            description.includes(searchTerm) ||
                            features.some(feature => feature.includes(searchTerm));

                        if (!matchesSearch) {
                            shouldShow = false;
                        }
                    }

                    // Show or hide the card
                    card.style.display = shouldShow ? 'block' : 'none';
                });

                // Show a message if no results found
                const visibleCards = Array.from(serviceCards).filter(card => card.style.display !== 'none');
                const noResultsMessage = activeTabContent.querySelector('.no-results-message');

                if (visibleCards.length === 0) {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.className = 'no-results-message text-center py-8';
                        message.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-lg font-medium text-gray-900">No services found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
                    `;
                        activeTabContent.querySelector('.grid').appendChild(message);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }

            function showAllServices() {
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const serviceCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow');

                serviceCards.forEach(card => {
                    card.style.display = 'block';
                });

                const noResultsMessage = activeTabContent.querySelector('.no-results-message');
                if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }

            // FAQ accordion functionality
            const faqButtons = document.querySelectorAll('[id^="faq-"][id$="-button"]');

            faqButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const expanded = button.getAttribute('aria-expanded') === 'true';
                    const panelId = button.getAttribute('aria-controls');
                    const panel = document.getElementById(panelId);

                    button.setAttribute('aria-expanded', !expanded);

                    if (expanded) {
                        panel.classList.add('hidden');
                    } else {
                        panel.classList.remove('hidden');
                    }

                    // Toggle the icon rotation
                    const icon = button.querySelector('svg');
                    if (expanded) {
                        icon.classList.remove('transform', 'rotate-180');
                    } else {
                        icon.classList.add('transform', 'rotate-180');
                    }
                });
            });
        });
    </script>
@endpush
