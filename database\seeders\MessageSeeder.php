<?php

namespace Database\Seeders;

use App\Models\Message;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class MessageSeeder extends Seeder
{
    public function run(): void
    {
        // Get users
        $client = User::where('email', '<EMAIL>')->first();
        $professional = User::where('email', '<EMAIL>')->first();
        $proSeller = User::where('email', '<EMAIL>')->first();
        
        if (!$client || !$professional || !$proSeller) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }
        
        // Get projects
        $projects = Project::all();
        
        if ($projects->isEmpty()) {
            $this->command->info('No projects found. Please run ProjectSeeder first.');
            return;
        }
        
        // Create messages
        // Project-related messages
        foreach ($projects as $index => $project) {
            if ($index % 2 == 0) {
                // Client to Professional
                Message::create([
                    'sender_id' => $project->client_id,
                    'recipient_id' => $project->professional_id,
                    'project_id' => $project->id,
                    'subject' => 'Updates on ' . $project->title,
                    'content' => 'Hi, I wanted to check on the progress of this project. Can you provide an update?',
                    'is_read' => true,
                    'created_at' => Carbon::now()->subDays(rand(1, 10))->subHours(rand(1, 23)),
                ]);
                
                // Professional to Client
                Message::create([
                    'sender_id' => $project->professional_id,
                    'recipient_id' => $project->client_id,
                    'project_id' => $project->id,
                    'subject' => 'RE: Updates on ' . $project->title,
                    'content' => 'Hello! We\'re making good progress. I\'ve completed the initial phase and will be sharing the first draft by tomorrow.',
                    'is_read' => false,
                    'created_at' => Carbon::now()->subDays(rand(1, 5))->subHours(rand(1, 12)),
                ]);
            } else {
                // Professional to Client
                Message::create([
                    'sender_id' => $project->professional_id,
                    'recipient_id' => $project->client_id,
                    'project_id' => $project->id,
                    'subject' => 'Question about ' . $project->title,
                    'content' => 'I have a few questions about the requirements for this project. Could we schedule a quick call to discuss?',
                    'is_read' => true,
                    'created_at' => Carbon::now()->subDays(rand(1, 10))->subHours(rand(1, 23)),
                ]);
                
                // Client to Professional
                Message::create([
                    'sender_id' => $project->client_id,
                    'recipient_id' => $project->professional_id,
                    'project_id' => $project->id,
                    'subject' => 'RE: Question about ' . $project->title,
                    'content' => 'Sure, I\'m available tomorrow at 2 PM. Let me know if that works for you.',
                    'is_read' => false,
                    'created_at' => Carbon::now()->subDays(rand(1, 5))->subHours(rand(1, 12)),
                ]);
            }
        }
        
        // General messages (not related to projects)
        Message::create([
            'sender_id' => $professional->id,
            'recipient_id' => $client->id,
            'subject' => 'New Service Offering',
            'content' => 'I wanted to let you know about a new service I\'m offering that might interest you. It\'s a comprehensive digital marketing package.',
            'is_read' => false,
            'created_at' => Carbon::now()->subDays(2),
        ]);
        
        Message::create([
            'sender_id' => $proSeller->id,
            'recipient_id' => $client->id,
            'subject' => 'Thank you for your business',
            'content' => 'I just wanted to thank you for your continued business. It\'s been a pleasure working with you on these projects.',
            'is_read' => true,
            'created_at' => Carbon::now()->subDays(7),
        ]);
    }
}
