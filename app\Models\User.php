<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, TwoFactorAuthenticatable;

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected function getDefaultCasts(): array
    {
        return [
            'id' => 'string',
        ];
    }

    /**
     * Boot function from <PERSON><PERSON>.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'account_type',
        'is_seller',
        'first_name',
        'last_name',
        'company',
        'specialty',
        'profile_image',
        'store_image',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id' => 'string',
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_seller' => 'boolean',
        'two_factor_confirmed_at' => 'datetime',
    ];

    /**
     * Get the client projects for the user.
     */
    public function clientProjects()
    {
        return $this->hasMany(Project::class, 'client_id');
    }

    /**
     * Get the professional projects for the user.
     */
    public function professionalProjects()
    {
        return $this->hasMany(Project::class, 'professional_id');
    }

    /**
     * Get the sent messages for the user.
     */
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get the received messages for the user.
     */
    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'recipient_id');
    }

    /**
     * Get the favorites for the user.
     */
    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * Get the favorited professionals for the user.
     */
    public function favoritedProfessionals()
    {
        return $this->belongsToMany(User::class, 'favorites', 'user_id', 'professional_id');
    }

    /**
     * Get the orders for the user.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the portfolio items for the user (professional).
     */
    public function portfolioItems()
    {
        return $this->hasMany(PortfolioItem::class, 'professional_id');
    }

    /**
     * Get the earnings for the user (professional).
     */
    public function earnings()
    {
        return $this->hasMany(Earning::class, 'professional_id');
    }

    /**
     * Get the products created by the user (seller).
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the professional record for the user.
     */
    public function professional()
    {
        return $this->hasOne(Professional::class);
    }

    /**
     * Get the seller applications for the user.
     */
    public function sellerApplications()
    {
        return $this->hasMany(SellerApplication::class);
    }
}
