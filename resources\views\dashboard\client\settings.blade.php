@extends('layouts.dashboard')

@section('title', 'Settings')

@section('header', 'Settings')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.client.projects') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.projects') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="{{ route('dashboard.client.messages') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.messages') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="{{ route('dashboard.client.favorites') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.favorites') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Favorites
        </a>
        <a href="{{ route('dashboard.client.orders') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.orders') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Orders
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client.profile') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.profile') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
        </a>
        <a href="{{ route('dashboard.client.settings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.settings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <a href="{{ route('home') }}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Back to Website
    </a>
</div>
@endsection

@section('mobile_sidebar')
<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="{{ route('dashboard.client') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
    <a href="{{ route('dashboard.client.projects') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
    <a href="{{ route('dashboard.client.messages') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
    <a href="{{ route('dashboard.client.favorites') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Favorites</a>
    <a href="{{ route('dashboard.client.orders') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Orders</a>
    <a href="{{ route('dashboard.client.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
    <a href="{{ route('dashboard.client.settings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
    <a href="{{ route('home') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
@endsection

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Account Settings</h2>
    <p class="mt-1 text-sm text-gray-500">Manage your account settings and preferences.</p>
</div>

@if(session('success'))
<div class="mb-6 rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
        </div>
    </div>
</div>
@endif

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Profile Information</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Update your account's profile information.</p>
    </div>
    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        <form action="{{ route('client.updateProfile') }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-3">
                    <label for="first_name" class="block text-sm font-medium text-gray-700">First name</label>
                    <input type="text" name="first_name" id="first_name" value="{{ old('first_name', $user->first_name) }}" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('first_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="col-span-6 sm:col-span-3">
                    <label for="last_name" class="block text-sm font-medium text-gray-700">Last name</label>
                    <input type="text" name="last_name" id="last_name" value="{{ old('last_name', $user->last_name) }}" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('last_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="col-span-6 sm:col-span-4">
                    <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $user->email) }}" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="col-span-6 sm:col-span-4">
                    <label for="company" class="block text-sm font-medium text-gray-700">Company</label>
                    <input type="text" name="company" id="company" value="{{ old('company', $user->company) }}" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('company')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    Save
                </button>
            </div>
        </form>
    </div>
</div>

<div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Change Password</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Ensure your account is using a secure password.</p>
    </div>
    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        <form action="{{ route('user-password.update') }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-4">
                    <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
                    <input type="password" name="current_password" id="current_password" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('current_password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="col-span-6 sm:col-span-4">
                    <label for="password" class="block text-sm font-medium text-gray-700">New Password</label>
                    <input type="password" name="password" id="password" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="col-span-6 sm:col-span-4">
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" class="mt-1 focus:ring-[#710d17] focus:border-[#710d17] block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    Save
                </button>
            </div>
        </form>
    </div>
</div>

<div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Two Factor Authentication</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Add additional security to your account using two factor authentication.</p>
    </div>
    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        @if(!$user->two_factor_confirmed_at)
        <h3 class="text-lg font-medium text-gray-900">You have not enabled two factor authentication.</h3>
        <div class="mt-3 max-w-xl text-sm text-gray-600">
            <p>When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.</p>
        </div>
        <div class="mt-5">
            <a href="{{ route('two-factor.enable') }}" class="inline-flex items-center px-4 py-2 bg-[#710d17] border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-[#9a2c39] active:bg-[#710d17] focus:outline-none focus:border-[#710d17] focus:ring focus:ring-[#710d17] disabled:opacity-25 transition">
                Enable
            </a>
        </div>
        @else
        <h3 class="text-lg font-medium text-gray-900">You have enabled two factor authentication.</h3>
        <div class="mt-3 max-w-xl text-sm text-gray-600">
            <p>Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.</p>
        </div>
        <div class="mt-5">
            <a href="{{ route('two-factor.disable') }}" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-600 focus:outline-none focus:border-red-600 focus:ring focus:ring-red-600 disabled:opacity-25 transition">
                Disable
            </a>
        </div>
        @endif
    </div>
</div>
@endsection
