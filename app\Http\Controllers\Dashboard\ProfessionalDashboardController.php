<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Earning;
use App\Models\Message;
use App\Models\PortfolioItem;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ProfessionalDashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('professional');
    }

    /**
     * Helper method to get or create a professional record for a user.
     *
     * @param  \App\Models\User  $user
     * @return \App\Models\Professional
     */
    protected function getOrCreateProfessional($user)
    {
        // Check if the user already has a professional record
        $professional = \App\Models\Professional::where('user_id', $user->id)->first();

        // If not, create one
        if (!$professional) {
            $professional = new \App\Models\Professional();
            $professional->user_id = $user->id;
            $professional->name = $user->name;
            $professional->title = $user->specialty ?? 'Professional';
            $professional->skills = ['General'];
            $professional->image = 'professionals/default.jpg';
            $professional->description = 'Professional profile';
            $professional->experience = '1 year';
            $professional->rating = 5.0;
            $professional->projects = 0;
            $professional->category = 'web'; // Default category
            $professional->is_aiverified = false;
            $professional->save();
        }

        return $professional;
    }

    /**
     * Show the professional dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();

        // Get real data from database
        $activeProjects = Project::where('professional_id', $user->id)
            ->whereIn('status', ['In Progress', 'Just Started', 'Pending Approval'])
            ->count();

        $completedProjects = Project::where('professional_id', $user->id)
            ->where('status', 'Completed')
            ->count();

        $unreadMessages = Message::where('recipient_id', $user->id)
            ->where('is_read', false)
            ->count();

        $totalEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Paid')
            ->sum('amount');

        $stats = [
            'active_projects' => $activeProjects,
            'completed_projects' => $completedProjects,
            'unread_messages' => $unreadMessages,
            'total_earnings' => $totalEarnings
        ];

        // Get recent projects with client information
        $recentProjects = Project::where('professional_id', $user->id)
            ->with('client')
            ->orderBy('updated_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'client' => $project->client->name,
                    'status' => $project->status,
                    'due_date' => $project->due_date->format('Y-m-d'),
                    'progress' => $project->progress
                ];
            });

        // Get recent messages with sender information
        $recentMessages = Message::where('recipient_id', $user->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'from' => $message->sender->name,
                    'subject' => $message->subject,
                    'preview' => $message->content,
                    'time' => $message->created_at->diffForHumans(),
                    'unread' => !$message->is_read
                ];
            });

        // Get portfolio highlights
        $portfolioHighlights = PortfolioItem::where('professional_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();

        return view('dashboard.professional.index', compact('user', 'stats', 'recentProjects', 'recentMessages', 'portfolioHighlights'));
    }

    /**
     * Show the professional projects page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function projects(Request $request)
    {
        $user = Auth::user();
        $status = $request->query('status');

        $query = Project::where('professional_id', $user->id)
            ->with('client')
            ->orderBy('updated_at', 'desc');

        // Apply status filter if provided and not 'All Projects'
        if ($status && $status !== 'All Projects') {
            $query->where('status', $status);
        }

        $projects = $query->get();

        // Get all possible statuses for the filter dropdown
        $statuses = Project::where('professional_id', $user->id)
            ->select('status')
            ->distinct()
            ->pluck('status')
            ->toArray();

        // Add 'All Projects' as the first option
        array_unshift($statuses, 'All Projects');

        return view('dashboard.professional.projects', compact('projects', 'statuses', 'status'));
    }

    /**
     * Show the professional messages page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function messages()
    {
        $user = Auth::user();

        // Get all messages (both sent and received)
        $allMessages = Message::where('recipient_id', $user->id)
            ->orWhere('sender_id', $user->id)
            ->with(['sender', 'recipient', 'project'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Group messages by conversation (unique combination of participants and project)
        $conversations = collect();
        $latestMessageByConversation = [];

        foreach ($allMessages as $message) {
            // Determine the other participant
            $otherParticipantId = ($message->sender_id == $user->id)
                ? $message->recipient_id
                : $message->sender_id;

            // Create a unique key for this conversation
            $projectId = $message->project_id ?? 'no_project';
            $conversationKey = $otherParticipantId . '_' . $projectId;

            // Track the latest message for each conversation
            if (!isset($latestMessageByConversation[$conversationKey]) ||
                $message->created_at->gt($latestMessageByConversation[$conversationKey]->created_at)) {
                $latestMessageByConversation[$conversationKey] = $message;
            }
        }

        // Add all latest messages to the conversations collection
        foreach ($latestMessageByConversation as $message) {
            $conversations->push($message);
        }

        // Sort conversations by the most recent message first
        $conversations = $conversations->sortByDesc(function($message) {
            return $message->created_at;
        });

        // Split into received and sent conversations
        $receivedMessages = $conversations->filter(function ($message) use ($user) {
            return $message->recipient_id == $user->id;
        });

        $sentMessages = $conversations->filter(function ($message) use ($user) {
            return $message->sender_id == $user->id;
        });

        return view('dashboard.professional.messages', compact('receivedMessages', 'sentMessages'));
    }

    /**
     * Show the form for creating a new message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createMessage(Request $request)
    {
        $user = Auth::user();

        // Get client_id and project_id from request if available
        $clientId = $request->query('client_id');
        $projectId = $request->query('project_id');

        $clients = User::where('account_type', 'client')->get();
        $projects = Project::where('professional_id', $user->id)->get();

        // If client_id is provided, get the specific client
        $selectedClient = null;
        if ($clientId) {
            $selectedClient = User::where('id', $clientId)->where('account_type', 'client')->first();
        }

        // If project_id is provided, get the specific project
        $selectedProject = null;
        if ($projectId) {
            $selectedProject = Project::where('id', $projectId)
                ->where('professional_id', $user->id)
                ->first();
        }

        return view('dashboard.professional.create-message', compact('clients', 'projects', 'selectedClient', 'selectedProject'));
    }

    /**
     * Store a new message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function storeMessage(Request $request)
    {
        $user = Auth::user();

        try {
            $validated = $request->validate([
                'recipient_id' => 'required|exists:users,id',
                'subject' => 'required|string|max:255',
                'content' => 'required|string',
                'project_id' => 'nullable|exists:projects,id',
                'message_id' => 'nullable|exists:messages,id',
            ]);

            // Create a new message with UUID
            $message = new Message();
            $message->id = (string) \Illuminate\Support\Str::uuid();
            $message->sender_id = $user->id;
            $message->recipient_id = $validated['recipient_id'];
            $message->subject = $validated['subject'];
            $message->content = $validated['content'];
            $message->project_id = $validated['project_id'] ?? null;
            $message->is_read = false;

            // Save to database
            $saved = $message->save();

            if (!$saved) {
                if ($request->ajax()) {
                    return response()->json(['success' => false, 'message' => 'Failed to send message']);
                }

                return redirect()->route('dashboard.professional.messages.create')
                    ->with('error', 'Failed to send message. Please try again.')
                    ->withInput();
            }

            // If this is an AJAX request (reply from message details page)
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Message sent successfully',
                    'messageId' => $message->id
                ]);
            }

            // If this is a regular form submission (from new message page)
            return redirect()->route('dashboard.professional.messages')
                ->with('success', 'Message sent successfully');

        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
            }

            return redirect()->route('dashboard.professional.messages.create')
                ->with('error', 'An error occurred while sending the message: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Check for new messages in a conversation.
     *
     * @param  string  $messageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkNewMessages($messageId)
    {
        $user = Auth::user();

        try {
            // Get the original message to identify the conversation
            $originalMessage = Message::findOrFail($messageId);

            // Determine the other participant
            $otherParticipantId = ($originalMessage->sender_id == $user->id)
                ? $originalMessage->recipient_id
                : $originalMessage->sender_id;

            // Check if there are any new messages from the other participant
            // that are newer than the last time the user loaded the page
            $hasNewMessages = Message::where('sender_id', $otherParticipantId)
                ->where('recipient_id', $user->id)
                ->where('created_at', '>', now()->subMinutes(1)) // Only check for messages in the last minute
                ->where('is_read', false)
                ->when($originalMessage->project_id, function($query) use ($originalMessage) {
                    return $query->where('project_id', $originalMessage->project_id);
                })
                ->exists();

            return response()->json(['hasNewMessages' => $hasNewMessages]);

        } catch (\Exception $e) {
            Log::error('Error checking for new messages: ' . $e->getMessage());
            return response()->json(['hasNewMessages' => false, 'error' => $e->getMessage()]);
        }
    }

    /**
     * Show the professional portfolio page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function portfolio()
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $portfolioItems = PortfolioItem::where('professional_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.professional.portfolio', compact('portfolioItems'));
    }

    /**
     * Show the professional earnings page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function earnings(Request $request)
    {
        $user = Auth::user();
        $status = $request->query('status');

        $query = Earning::where('professional_id', $user->id)
            ->with(['project', 'order'])
            ->orderBy('created_at', 'desc');

        // Apply status filter if provided and not 'All Earnings'
        if ($status && $status !== 'All Earnings') {
            $query->where('status', $status);
        }

        $earnings = $query->get();

        $totalEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Paid')
            ->sum('amount');

        $pendingEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Pending')
            ->sum('amount');

        // Get all possible statuses for the filter dropdown
        $statuses = ['All Earnings', 'Paid', 'Pending', 'Cancelled'];

        return view('dashboard.professional.earnings', compact('earnings', 'totalEarnings', 'pendingEarnings', 'statuses', 'status'));
    }



    /**
     * Show the professional profile page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = Auth::user();

        return view('dashboard.professional.profile', compact('user'));
    }

    /**
     * Show the professional settings page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function settings()
    {
        $user = Auth::user();

        return view('dashboard.professional.settings', compact('user'));
    }

    /**
     * Update the professional profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'specialty' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        // Update user data
        User::where('id', $user->id)->update([
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'name' => $validated['first_name'] . ' ' . $validated['last_name'],
            'specialty' => $validated['specialty'],
            'bio' => $validated['bio'],
            'email' => $validated['email']
        ]);

        return redirect()->route('dashboard.professional.profile')->with('success', 'Profile updated successfully');
    }

    /**
     * Show the form for adding a portfolio item.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createPortfolioItem()
    {
        $categories = \App\Models\Category::all();
        return view('dashboard.professional.create-portfolio-item', compact('categories'));
    }

    /**
     * Store a new portfolio item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storePortfolioItem(Request $request)
    {
        $user = Auth::user();

        // Ensure the user has a professional record
        $this->getOrCreateProfessional($user);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'client_name' => 'nullable|string|max:255',
            'completion_date' => 'nullable|date',
            'image' => 'nullable|image|max:2048',
            'featured' => 'boolean',
        ]);

        $portfolioItem = new PortfolioItem();
        $portfolioItem->professional_id = $user->id;
        $portfolioItem->title = $validated['title'];
        $portfolioItem->description = $validated['description'];
        $portfolioItem->category = $validated['category'];
        $portfolioItem->client_name = $validated['client_name'];
        $portfolioItem->completion_date = $validated['completion_date'];
        $portfolioItem->featured = $request->has('featured');

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('portfolio', 'public');
            $portfolioItem->image = $imagePath;
        }

        $portfolioItem->save();

        return redirect()->route('dashboard.professional.portfolio')->with('success', 'Portfolio item added successfully');
    }

    /**
     * Show a specific portfolio item.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showPortfolioItem($id)
    {
        $user = Auth::user();

        // Ensure the user has a professional record
        $this->getOrCreateProfessional($user);

        $portfolioItem = PortfolioItem::where('id', $id)
            ->where('professional_id', $user->id)
            ->firstOrFail();

        return view('dashboard.professional.show-portfolio-item', compact('portfolioItem'));
    }

    /**
     * Show the form for editing a portfolio item.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function editPortfolioItem($id)
    {
        $user = Auth::user();

        // Ensure the user has a professional record
        $this->getOrCreateProfessional($user);

        $portfolioItem = PortfolioItem::where('id', $id)
            ->where('professional_id', $user->id)
            ->firstOrFail();

        $categories = \App\Models\Category::all();

        return view('dashboard.professional.edit-portfolio-item', compact('portfolioItem', 'categories'));
    }

    /**
     * Update a portfolio item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePortfolioItem(Request $request, $id)
    {
        $user = Auth::user();

        // Ensure the user has a professional record
        $this->getOrCreateProfessional($user);

        $portfolioItem = PortfolioItem::where('id', $id)
            ->where('professional_id', $user->id)
            ->firstOrFail();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'client_name' => 'nullable|string|max:255',
            'completion_date' => 'nullable|date',
            'image' => 'nullable|image|max:2048',
            'featured' => 'boolean',
        ]);

        $portfolioItem->title = $validated['title'];
        $portfolioItem->description = $validated['description'];
        $portfolioItem->category = $validated['category'];
        $portfolioItem->client_name = $validated['client_name'];
        $portfolioItem->completion_date = $validated['completion_date'];
        $portfolioItem->featured = $request->has('featured');

        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($portfolioItem->image && file_exists(public_path($portfolioItem->image))) {
                unlink(public_path($portfolioItem->image));
            }

            $imagePath = $request->file('image')->store('portfolio', 'public');
            $portfolioItem->image = $imagePath;
        }

        $portfolioItem->save();

        return redirect()->route('dashboard.professional.portfolio')->with('success', 'Portfolio item updated successfully');
    }

    /**
     * Delete a portfolio item.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyPortfolioItem($id)
    {
        $user = Auth::user();

        // Ensure the user has a professional record
        $this->getOrCreateProfessional($user);

        $portfolioItem = PortfolioItem::where('id', $id)
            ->where('professional_id', $user->id)
            ->firstOrFail();

        // Delete image if it exists
        if ($portfolioItem->image && file_exists(public_path($portfolioItem->image))) {
            unlink(public_path($portfolioItem->image));
        }

        $portfolioItem->delete();

        return redirect()->route('dashboard.professional.portfolio')->with('success', 'Portfolio item deleted successfully');
    }

    /**
     * Show the form for updating availability.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function updateAvailability()
    {
        $user = Auth::user();
        return view('dashboard.professional.update-availability', compact('user'));
    }

    /**
     * Store the updated availability.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeAvailability(Request $request)
    {
        // Validate the request
        $request->validate([
            'available' => 'required|boolean',
            'available_from' => 'nullable|date',
            'available_until' => 'nullable|date|after_or_equal:available_from',
        ]);

        // In a real application, you would update the user's availability in the database
        // For now, we'll just redirect with a success message

        return redirect()->route('dashboard.professional')->with('success', 'Availability updated successfully');
    }

    /**
     * Show the form for updating pricing.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function updatePricing()
    {
        $user = Auth::user();
        return view('dashboard.professional.update-pricing', compact('user'));
    }

    /**
     * Store the updated pricing.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storePricing(Request $request)
    {
        // Validate the request
        $request->validate([
            'hourly_rate' => 'nullable|numeric|min:0',
            'project_rate' => 'nullable|numeric|min:0',
        ]);

        // In a real application, you would update the user's pricing in the database
        // For now, we'll just redirect with a success message

        return redirect()->route('dashboard.professional')->with('success', 'Pricing updated successfully');
    }

    /**
     * Show the form for scheduling availability.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function scheduleAvailability()
    {
        $user = Auth::user();
        return view('dashboard.professional.schedule-availability', compact('user'));
    }

    /**
     * Store the scheduled availability.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSchedule(Request $request)
    {
        // Validate the request
        $request->validate([
            'schedule_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
        ]);

        // In a real application, you would store the user's schedule in the database
        // For now, we'll just redirect with a success message

        return redirect()->route('dashboard.professional')->with('success', 'Availability scheduled successfully');
    }

    /**
     * Show a specific project.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showProject($id)
    {
        $user = Auth::user();

        $project = Project::where('id', $id)
            ->where('professional_id', $user->id)
            ->with('client')
            ->firstOrFail();

        return view('dashboard.professional.project-details', compact('project'));
    }

    /**
     * Show a specific message.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showMessage($id)
    {
        $user = Auth::user();

        $message = Message::where('id', $id)
            ->where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->with(['sender', 'recipient', 'project'])
            ->firstOrFail();

        // Mark the message as read if user is the recipient
        if ($message->recipient_id === $user->id && !$message->is_read) {
            $message->is_read = true;
            $message->save();
        }

        // Get related messages (conversation)
        // Fetch all messages between the same participants and with the same project_id if applicable
        $otherParticipantId = ($message->sender_id == $user->id) ? $message->recipient_id : $message->sender_id;

        $relatedMessages = Message::where('id', '!=', $message->id)
            ->where(function($query) use ($user, $otherParticipantId) {
                $query->where(function($q) use ($user, $otherParticipantId) {
                    $q->where('sender_id', $user->id)
                      ->where('recipient_id', $otherParticipantId);
                })->orWhere(function($q) use ($user, $otherParticipantId) {
                    $q->where('sender_id', $otherParticipantId)
                      ->where('recipient_id', $user->id);
                });
            })
            ->when($message->project_id, function($query) use ($message) {
                return $query->where('project_id', $message->project_id);
            })
            ->with(['sender', 'recipient'])
            ->orderBy('created_at', 'asc')
            ->get();

        return view('dashboard.professional.message-details', compact('message', 'relatedMessages'));
    }

    /**
     * Show the professional products page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function products()
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $products = \App\Models\Product::where('professional_id', $professional->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.professional.products', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createProduct()
    {
        $categories = \App\Models\Category::all();
        return view('dashboard.professional.create-product', compact('categories'));
    }

    /**
     * Store a new product.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeProduct(Request $request)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'image' => 'nullable|image|max:2048',
        ]);

        $slug = \Illuminate\Support\Str::slug($validated['name']);

        $product = new \App\Models\Product();
        $product->professional_id = $professional->id;
        $product->user_id = $user->id;
        $product->name = $validated['name'];
        $product->slug = $slug;
        $product->price = $validated['price'];
        $product->description = $validated['description'];
        $product->category = $validated['category'];
        $product->rating = 0;
        $product->sales = 0;

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        $product->save();

        return redirect()->route('dashboard.professional.products')->with('success', 'Product added successfully');
    }

    /**
     * Show a specific product.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showProduct($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $product = \App\Models\Product::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        return view('dashboard.professional.show-product', compact('product'));
    }

    /**
     * Show the form for editing a product.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function editProduct($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $product = \App\Models\Product::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        $categories = \App\Models\Category::all();

        return view('dashboard.professional.edit-product', compact('product', 'categories'));
    }

    /**
     * Update a product.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProduct(Request $request, $id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $product = \App\Models\Product::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'image' => 'nullable|image|max:2048',
        ]);

        $product->name = $validated['name'];
        $product->price = $validated['price'];
        $product->description = $validated['description'];
        $product->category = $validated['category'];

        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($product->image && file_exists(public_path($product->image))) {
                unlink(public_path($product->image));
            }

            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        $product->save();

        return redirect()->route('dashboard.professional.products')->with('success', 'Product updated successfully');
    }

    /**
     * Delete a product.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyProduct($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $product = \App\Models\Product::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        // Delete image if it exists
        if ($product->image && file_exists(public_path($product->image))) {
            unlink(public_path($product->image));
        }

        $product->delete();

        return redirect()->route('dashboard.professional.products')->with('success', 'Product deleted successfully');
    }

    /**
     * Show the professional services page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function services()
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $services = \App\Models\Service::where('professional_id', $professional->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.professional.services', compact('services'));
    }

    /**
     * Show the form for creating a new service.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createService()
    {
        $categories = \App\Models\Category::all();
        return view('dashboard.professional.create-service', compact('categories'));
    }

    /**
     * Store a new service.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeService(Request $request)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'features' => 'nullable|array',
            'features.*' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
        ]);

        $slug = \Illuminate\Support\Str::slug($validated['title']);

        $service = new \App\Models\Service();
        $service->professional_id = $professional->id;
        $service->title = $validated['title'];
        $service->slug = $slug;
        $service->description = $validated['description'];
        $service->category = $validated['category'];

        // Filter out empty features
        $features = array_filter($validated['features'] ?? [], function($feature) {
            return !empty($feature);
        });

        $service->features = $features;
        $service->icon = $validated['icon'];

        $service->save();

        return redirect()->route('dashboard.professional.services')->with('success', 'Service added successfully');
    }

    /**
     * Show a specific service.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showService($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $service = \App\Models\Service::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        return view('dashboard.professional.show-service', compact('service'));
    }

    /**
     * Show the form for editing a service.
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function editService($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $service = \App\Models\Service::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        $categories = \App\Models\Category::all();

        return view('dashboard.professional.edit-service', compact('service', 'categories'));
    }

    /**
     * Update a service.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateService(Request $request, $id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $service = \App\Models\Service::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'features' => 'nullable|array',
            'features.*' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
        ]);

        $service->title = $validated['title'];
        $service->description = $validated['description'];
        $service->category = $validated['category'];

        // Filter out empty features
        $features = array_filter($validated['features'] ?? [], function($feature) {
            return !empty($feature);
        });

        $service->features = $features;
        $service->icon = $validated['icon'];

        $service->save();

        return redirect()->route('dashboard.professional.services')->with('success', 'Service updated successfully');
    }

    /**
     * Delete a service.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyService($id)
    {
        $user = Auth::user();

        // Get or create a professional record for this user
        $professional = $this->getOrCreateProfessional($user);

        $service = \App\Models\Service::where('id', $id)
            ->where('professional_id', $professional->id)
            ->firstOrFail();

        $service->delete();

        return redirect()->route('dashboard.professional.services')->with('success', 'Service deleted successfully');
    }
}
