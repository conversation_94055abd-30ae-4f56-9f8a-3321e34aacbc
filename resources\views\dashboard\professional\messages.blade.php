@extends('layouts.dashboard')

@section('title', 'Messages')

@section('header', 'Messages')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.professional') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.professional.projects') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.projects') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="{{ route('dashboard.professional.messages') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.messages') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="{{ route('dashboard.professional.portfolio') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.portfolio') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.portfolio') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Portfolio
        </a>
        <a href="{{ route('dashboard.professional.products.create') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.products*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.products*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Products
        </a>
        <a href="{{ route('dashboard.professional.services.create') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.services*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.services*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Services
        </a>
        <a href="{{ route('dashboard.professional.earnings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.earnings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Earnings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.professional.profile') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.profile') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
        </a>
        <a href="{{ route('dashboard.professional.settings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.settings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <a href="{{ route('home') }}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Back to Website
    </a>
</div>
@endsection

@section('mobile_sidebar')
<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="{{ route('dashboard.professional') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
    <a href="{{ route('dashboard.professional.projects') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
    <a href="{{ route('dashboard.professional.messages') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
    <a href="{{ route('dashboard.professional.portfolio') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.portfolio') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Portfolio</a>
    <a href="{{ route('dashboard.professional.earnings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Earnings</a>
    <a href="{{ route('dashboard.professional.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
    <a href="{{ route('dashboard.professional.settings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
    <a href="{{ route('home') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
@endsection

@section('content')
@if(session('success'))
<div class="mb-4 rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
        </div>
        <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
@endif

<div class="mb-6">
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Messages</h2>
        <a href="{{ route('dashboard.professional.messages.create') }}"
            class="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span>New Message</span>
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="mb-4">
    <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
        </div>
        <input type="text" id="search-conversations" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm" placeholder="Search conversations...">
    </div>
</div>

<!-- Conversations List -->
<div class="space-y-4">
    @php
        // Combine received and sent messages into a single collection and sort by most recent first
        $allMessages = collect()->merge($receivedMessages)->merge($sentMessages)->sortByDesc('created_at');
    @endphp

    @forelse($allMessages as $message)
    @php
        $isReceived = $message->recipient_id == Auth::id();
        $otherPerson = $isReceived ? $message->sender : $message->recipient;
        $unread = $isReceived && !$message->is_read;
    @endphp
    <div class="bg-white shadow rounded-lg overflow-hidden {{ $unread ? 'border-l-4 border-[#710d17]' : '' }}">
        <a href="{{ route('dashboard.professional.messages.show', $message->id) }}" class="block hover:bg-gray-50">
            <!-- Message header with avatar and name -->
            <div class="px-4 py-3 border-b border-gray-100 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                            <span class="text-sm font-medium">{{ substr($otherPerson->name, 0, 1) }}</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-base font-medium {{ $unread ? 'text-[#710d17]' : 'text-gray-900' }}">
                            {{ $otherPerson->name }}
                        </h3>
                    </div>
                </div>
                <div class="ml-2 flex-shrink-0 flex">
                    <span class="text-xs text-gray-500">{{ $message->created_at->diffForHumans() }}</span>
                </div>
            </div>

            <!-- Message content preview -->
            <div class="px-4 py-3">
                <div class="flex flex-col">
                    <p class="text-sm font-medium {{ $unread ? 'text-gray-900' : 'text-gray-700' }} truncate">
                        {{ preg_replace('/^(Re: )+/', '', $message->subject) }}
                    </p>
                    <p class="text-sm text-gray-500 line-clamp-2 mt-1">
                        {{ Str::limit($message->content, 120) }}
                    </p>
                </div>
            </div>

            <!-- Message tags and status -->
            <div class="px-4 py-2 bg-gray-50 border-t border-gray-100">
                <div class="flex flex-wrap gap-2">
                    @if($unread)
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            <svg class="mr-0.5 h-3 w-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            New
                        </span>
                    @endif

                    @if($message->project)
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            <svg class="mr-0.5 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            {{ Str::limit($message->project->title, 20) }}
                        </span>
                    @endif

                    @if(!$isReceived)
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            <svg class="mr-0.5 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            Sent
                        </span>
                    @endif

                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="mr-0.5 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                        </svg>
                        Conversation
                    </span>
                </div>
            </div>
        </a>
    </div>
    @empty
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-4 py-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <p class="mt-4 text-gray-500 text-lg">No conversations yet</p>
            <p class="text-gray-400 text-sm">Start a new conversation by clicking the "New Message" button</p>
            <div class="mt-6">
                <a href="{{ route('dashboard.professional.messages.create') }}"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Start a conversation
                </a>
            </div>
        </div>
    </div>
    @endforelse
</div>

<script>
    // Simple search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-conversations');
        const conversationsList = document.querySelector('.space-y-4');
        const conversations = conversationsList.querySelectorAll('.bg-white');

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            conversations.forEach(conversation => {
                const text = conversation.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    conversation.style.display = '';
                } else {
                    conversation.style.display = 'none';
                }
            });
        });
    });
</script>
@endsection
