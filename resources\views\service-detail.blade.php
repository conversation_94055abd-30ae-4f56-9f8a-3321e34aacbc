@extends('layouts.app')

@section('title', $service->title . ' - Hermosart Services')

@section('content')
<div class="bg-white">
    <!-- Hero Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h1 class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc]">
                        {{ $service->title }}
                    </h1>
                    <p class="max-w-[700px] text-zinc-200 md:text-xl">
                        {{ $service->description }}
                    </p>
                </div>
                <div class="w-full max-w-sm space-y-2">
                    <a href="#contact" class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Get Started
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Details Section -->
    <section class="w-full py-12 md:py-24 lg:py-32">
        <div class="container mx-auto px-4 md:px-6">
            <div class="mx-auto max-w-3xl">
                <!-- Back button -->
                <div class="mb-8">
                    <a href="{{ route('services') }}" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Services
                    </a>
                </div>

                <!-- Service overview -->
                <div class="mb-12">
                    <div class="flex items-center gap-4 mb-6">
                        <div class="p-3 bg-[#fcefcc] rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8">
                                @if($service->icon === 'code')
                                    <polyline points="16 18 22 12 16 6"></polyline>
                                    <polyline points="8 6 2 12 8 18"></polyline>
                                @elseif($service->icon === 'database')
                                    <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                                    <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                    <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                @elseif($service->icon === 'cpu')
                                    <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                                    <rect x="9" y="9" width="6" height="6"></rect>
                                    <line x1="9" y1="1" x2="9" y2="4"></line>
                                    <line x1="15" y1="1" x2="15" y2="4"></line>
                                    <line x1="9" y1="20" x2="9" y2="23"></line>
                                    <line x1="15" y1="20" x2="15" y2="23"></line>
                                    <line x1="20" y1="9" x2="23" y2="9"></line>
                                    <line x1="20" y1="14" x2="23" y2="14"></line>
                                    <line x1="1" y1="9" x2="4" y2="9"></line>
                                    <line x1="1" y1="14" x2="4" y2="14"></line>
                                @elseif($service->icon === 'palette')
                                    <circle cx="13.5" cy="6.5" r=".5"></circle>
                                    <circle cx="17.5" cy="10.5" r=".5"></circle>
                                    <circle cx="8.5" cy="7.5" r=".5"></circle>
                                    <circle cx="6.5" cy="12.5" r=".5"></circle>
                                    <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"></path>
                                @endif
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-2xl font-bold text-gray-900">{{ $service->title }}</h2>
                            @if($service->price > 0)
                                <p class="mt-2 text-3xl font-bold text-[#710d17]">${{ number_format($service->price, 2) }}</p>
                                @if($service->sales > 0)
                                    <p class="text-sm text-gray-600">{{ $service->sales }} {{ $service->sales == 1 ? 'sale' : 'sales' }}</p>
                                @endif
                            @endif
                        </div>
                    </div>

                    <div class="prose prose-lg max-w-none">
                        <p>{{ $service->description }}</p>
                    </div>
                </div>

                <!-- Professional Information Section -->
                @if($service->professional)
                <div class="mb-12 bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">About the Professional</h3>

                    <div class="flex flex-col md:flex-row gap-6">
                        <!-- Professional Avatar and Basic Info -->
                        <div class="flex-shrink-0">
                            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>

                            <!-- Rating and Verification -->
                            <div class="text-center">
                                @if($service->professional->rating)
                                    <div class="flex items-center justify-center mb-2">
                                        @for($i = 1; $i <= 5; $i++)
                                            <svg class="w-4 h-4 {{ $i <= $service->professional->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">{{ number_format($service->professional->rating, 1) }} out of 5</p>
                                @endif

                                @if($service->professional->is_aiverified)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        AI Verified
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Professional Details -->
                        <div class="flex-1">
                            <div class="mb-4">
                                <h4 class="text-lg font-bold text-gray-900">{{ $service->professional->name }}</h4>
                                <p class="text-gray-600">{{ $service->professional->title }}</p>
                                @if($service->professional->experience)
                                    <p class="text-sm text-gray-500">{{ $service->professional->experience }} of experience</p>
                                @endif
                            </div>

                            @if($service->professional->description)
                                <div class="mb-4">
                                    <p class="text-gray-700">{{ $service->professional->description }}</p>
                                </div>
                            @endif

                            <!-- Skills -->
                            @if($service->professional->skills && count($service->professional->skills) > 0)
                                <div class="mb-4">
                                    <h5 class="text-sm font-semibold text-gray-900 mb-2">Skills & Expertise</h5>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($service->professional->skills as $skill)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#fcefcc] text-[#710d17]">
                                                {{ $skill }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Stats -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                                @if($service->professional->projects)
                                    <div class="bg-white p-3 rounded-lg">
                                        <div class="text-lg font-bold text-[#710d17]">{{ $service->professional->projects }}</div>
                                        <div class="text-xs text-gray-500">Projects Completed</div>
                                    </div>
                                @endif

                                <div class="bg-white p-3 rounded-lg">
                                    <div class="text-lg font-bold text-[#710d17]">{{ $service->sales ?? 0 }}</div>
                                    <div class="text-xs text-gray-500">Service Sales</div>
                                </div>

                                @if($service->professional->rating)
                                    <div class="bg-white p-3 rounded-lg">
                                        <div class="text-lg font-bold text-[#710d17]">{{ number_format($service->professional->rating, 1) }}</div>
                                        <div class="text-xs text-gray-500">Average Rating</div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Portfolio Preview -->
                    @if($service->professional->portfolioItems && count($service->professional->portfolioItems) > 0)
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h5 class="text-sm font-semibold text-gray-900 mb-3">Recent Work</h5>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                @foreach($service->professional->portfolioItems as $portfolio)
                                    <div class="bg-white rounded-lg overflow-hidden shadow-sm">
                                        @if($portfolio->image)
                                            <div class="aspect-video bg-gray-200 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        @endif
                                        <div class="p-3">
                                            <h6 class="text-sm font-medium text-gray-900 truncate">{{ $portfolio->title }}</h6>
                                            @if($portfolio->client_name)
                                                <p class="text-xs text-gray-500">Client: {{ $portfolio->client_name }}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
                @endif

                <!-- Key features -->
                <div class="mb-12">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Key Features</h3>
                    <div class="grid gap-4 md:grid-cols-2">
                        @foreach($service->features as $feature)
                            <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                                <div class="flex items-center gap-3">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#710d17]" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-800">{{ $feature }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Benefits -->
                <div class="mb-12">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Benefits</h3>
                    <div class="bg-[#fcefcc]/20 p-6 rounded-lg">
                        <ul class="space-y-4">
                            <li class="flex items-start gap-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#710d17] flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <div>
                                    <h4 class="font-medium text-gray-900">Expert Delivery</h4>
                                    <p class="text-gray-600">Our AI-verified professionals ensure high-quality results.</p>
                                </div>
                            </li>
                            <li class="flex items-start gap-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#710d17] flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <div>
                                    <h4 class="font-medium text-gray-900">Timely Completion</h4>
                                    <p class="text-gray-600">We adhere to agreed timelines and keep you updated throughout the process.</p>
                                </div>
                            </li>
                            <li class="flex items-start gap-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#710d17] flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <div>
                                    <h4 class="font-medium text-gray-900">Ongoing Support</h4>
                                    <p class="text-gray-600">We provide continued assistance and maintenance after project completion.</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Trust & Security Section -->
                <div class="mb-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-blue-900 mb-4">Why Choose Hermosart?</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">AI-Verified Professionals</h4>
                                    <p class="text-sm text-blue-800">All our professionals undergo rigorous AI-powered verification to ensure top-quality service delivery.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">Secure Payments</h4>
                                    <p class="text-sm text-blue-800">Your payments are protected with industry-standard encryption and processed through secure PayPal integration.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">Fast Delivery</h4>
                                    <p class="text-sm text-blue-800">Our professionals are committed to delivering high-quality work within agreed timelines.</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">24/7 Support</h4>
                                    <p class="text-sm text-blue-800">Our dedicated support team is available around the clock to assist you with any questions or concerns.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">Quality Guarantee</h4>
                                    <p class="text-sm text-blue-800">We stand behind our work with a satisfaction guarantee and revision policy to ensure you get exactly what you need.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-900">Trusted by 1000+ Clients</h4>
                                    <p class="text-sm text-blue-800">Join thousands of satisfied clients who have successfully completed projects through our platform.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Purchase/CTA Section -->
                <div class="bg-[#710d17] text-white p-8 rounded-lg text-center mb-12">
                    <h3 class="text-xl font-bold mb-2">Ready to get started?</h3>
                    @if($service->price > 0)
                        <p class="mb-6">Purchase this service now for ${{ number_format($service->price, 2) }}</p>
                        <div class="flex gap-4 justify-center">
                            @auth
                                @if(auth()->user()->id !== $service->professional_id)
                                    <form action="{{ route('payment.service.initiate', $service->id) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="inline-flex items-center justify-center rounded-md bg-white px-6 py-3 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc] transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v6" />
                                            </svg>
                                            Buy Now with PayPal
                                        </button>
                                    </form>
                                @else
                                    <p class="text-[#fcefcc] text-sm">This is your own service</p>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="inline-flex items-center justify-center rounded-md bg-white px-6 py-3 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc] transition-colors">
                                    Login to Purchase
                                </a>
                            @endauth
                            <a href="#contact" class="inline-flex items-center justify-center rounded-md border border-white px-6 py-3 text-sm font-medium text-white hover:bg-white hover:text-[#710d17] transition-colors">
                                Contact for Custom Quote
                            </a>
                        </div>
                    @else
                        <p class="mb-6">Contact us today to discuss your project requirements and get a custom quote.</p>
                        <a href="#contact" class="inline-flex items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc] transition-colors">
                            Request a Quote
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Related Case Studies Section -->
    @if(count($relatedCaseStudies) > 0)
    <section class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Related Case Studies
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        See how we've helped businesses with similar services.
                    </p>
                </div>
            </div>

            <div class="grid gap-8 md:grid-cols-3">
                @foreach($relatedCaseStudies as $caseStudy)
                    <div class="overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                        <div class="aspect-video overflow-hidden">
                            <img src="{{ asset($caseStudy->image) }}" alt="{{ $caseStudy->title }}" class="h-full w-full object-cover transition-transform hover:scale-105">
                        </div>
                        <div class="p-6">
                            <div class="mb-2">
                                <span class="inline-flex items-center rounded-md bg-[#fcefcc]/30 px-2 py-1 text-xs font-medium text-[#710d17]">
                                    {{ ucfirst($caseStudy->category) }}
                                </span>
                            </div>
                            <h3 class="text-xl font-bold">{{ $caseStudy->title }}</h3>
                            <p class="mt-1 text-sm text-zinc-500">Client: {{ $caseStudy->client }}</p>
                            <p class="mt-3 text-zinc-600">{{ $caseStudy->description }}</p>
                            <div class="mt-4">
                                <a href="#" class="text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                                    Read full case study →
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Related Services Section -->
    @if(count($relatedServices) > 0)
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Related Services
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Explore other services that might interest you.
                    </p>
                </div>
            </div>

            <div class="grid gap-6 md:grid-cols-3">
                @foreach($relatedServices as $relatedService)
                    <div class="overflow-hidden rounded-lg border bg-white shadow">
                        <div class="p-6">
                            <div class="flex items-center gap-4">
                                <div class="p-2 bg-[#fcefcc] rounded-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                                        @if($relatedService->icon === 'code')
                                            <polyline points="16 18 22 12 16 6"></polyline>
                                            <polyline points="8 6 2 12 8 18"></polyline>
                                        @elseif($relatedService->icon === 'database')
                                            <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                                            <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                            <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                        @elseif($relatedService->icon === 'cpu')
                                            <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                                            <rect x="9" y="9" width="6" height="6"></rect>
                                            <line x1="9" y1="1" x2="9" y2="4"></line>
                                            <line x1="15" y1="1" x2="15" y2="4"></line>
                                            <line x1="9" y1="20" x2="9" y2="23"></line>
                                            <line x1="15" y1="20" x2="15" y2="23"></line>
                                            <line x1="20" y1="9" x2="23" y2="9"></line>
                                            <line x1="20" y1="14" x2="23" y2="14"></line>
                                            <line x1="1" y1="9" x2="4" y2="9"></line>
                                            <line x1="1" y1="14" x2="4" y2="14"></line>
                                        @elseif($relatedService->icon === 'palette')
                                            <circle cx="13.5" cy="6.5" r=".5"></circle>
                                            <circle cx="17.5" cy="10.5" r=".5"></circle>
                                            <circle cx="8.5" cy="7.5" r=".5"></circle>
                                            <circle cx="6.5" cy="12.5" r=".5"></circle>
                                            <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"></path>
                                        @endif
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold">{{ $relatedService->title }}</h3>
                                    <p class="mt-1 text-zinc-600">{{ $relatedService->description }}</p>
                                    @if($relatedService->price > 0)
                                        <p class="mt-2 text-lg font-bold text-[#710d17]">${{ number_format($relatedService->price, 2) }}</p>
                                    @endif

                                    <!-- Professional Info for Related Services -->
                                    @if($relatedService->professional)
                                        <div class="mt-2 flex items-center gap-2">
                                            <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate">{{ $relatedService->professional->name }}</p>
                                                <div class="flex items-center gap-1">
                                                    @if($relatedService->professional->rating)
                                                        <div class="flex items-center">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <svg class="w-3 h-3 {{ $i <= $relatedService->professional->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                            @endfor
                                                            <span class="text-xs text-gray-500 ml-1">({{ number_format($relatedService->professional->rating, 1) }})</span>
                                                        </div>
                                                    @endif
                                                    @if($relatedService->professional->is_aiverified)
                                                        <span class="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
                                                            <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                            </svg>
                                                            Verified
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="mt-6">
                                <a href="{{ route('services.show', $relatedService->slug ?? $relatedService->id) }}" class="inline-flex w-full items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                    Learn More
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Contact Section -->
    <section id="contact" class="w-full py-12 md:py-24 lg:py-32 bg-[#fcefcc]/30">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Get in Touch
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Contact us today to discuss your {{ $service->title }} needs.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-3xl">
                <div class="overflow-hidden rounded-lg border bg-white shadow">
                    <div class="p-6">
                        <form class="space-y-4">
                            <div class="grid gap-4 md:grid-cols-2">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input type="text" id="name" name="name" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]" required>
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="email" name="email" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]" required>
                                </div>
                            </div>
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">Company (optional)</label>
                                <input type="text" id="company" name="company" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]">
                            </div>
                            <input type="hidden" name="service" value="{{ $service->title }}">
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                                <textarea id="message" name="message" rows="4" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#710d17] focus:border-[#710d17]" required></textarea>
                            </div>
                            <div>
                                <button type="submit" class="inline-flex w-full items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                    Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
