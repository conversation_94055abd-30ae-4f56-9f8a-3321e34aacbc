<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        // Get client user
        $client = User::where('email', '<EMAIL>')->first();
        
        if (!$client) {
            $this->command->info('Client user not found. Please run UserSeeder first.');
            return;
        }
        
        // Get products
        $products = Product::all();
        
        if ($products->isEmpty()) {
            $this->command->info('No products found. Please run ProductSeeder first.');
            return;
        }
        
        // Create orders
        foreach ($products->take(3) as $index => $product) {
            $status = ['Completed', 'Processing', 'Pending'][$index % 3];
            
            Order::create([
                'user_id' => $client->id,
                'product_id' => $product->id,
                'price' => $product->price,
                'status' => $status,
                'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
        }
    }
}
