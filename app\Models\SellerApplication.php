<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SellerApplication extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'business_name',
        'business_name_slug',
        'business_description',
        'phone_number',
        'paypal_email',
        'status',
        'rejection_reason',
        'approved_at'
    ];

    protected $casts = [
        'id' => 'string',
        'user_id' => 'string',
        'approved_at' => 'datetime',
    ];

    /**
     * Boot function from Laravel.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) \Illuminate\Support\Str::uuid();
            }

            if (empty($model->business_name_slug) && !empty($model->business_name)) {
                $model->business_name_slug = \Illuminate\Support\Str::slug($model->business_name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('business_name') && !$model->isDirty('business_name_slug')) {
                $model->business_name_slug = \Illuminate\Support\Str::slug($model->business_name);
            }
        });
    }

    /**
     * Get the user that owns the application.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
