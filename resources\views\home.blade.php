@extends('layouts.app')

@section('title', 'Hermosart - AI-Powered Talent Agency')

@section('content')
    <!-- Hero Section -->
    <section class="relative w-full py-8 md:py-24 lg:py-32 xl:py-48 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-[#710d17] to-[#9a2c39] z-0"></div>
        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <div class="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
                <div class="flex flex-col justify-center space-y-4">
                    <div class="space-y-2">
                        <h1
                            class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc] animate-fade-in">
                            Empower Your Future with Hermosart: Talent Meets Technology
                        </h1>
                        <p class="max-w-[600px] text-zinc-200 md:text-xl animate-fade-in" style="animation-delay: 0.2s;">
                            Discover AI-powered tech talent and premium digital products to bring your ideas to life.
                        </p>
                    </div>
                    <div class="flex flex-col gap-2 min-[400px]:flex-row animate-fade-in" style="animation-delay: 0.4s;">
                        <a href="#talent"
                            class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3] btn-hover">
                            Hire Top Talent
                        </a>
                        <a href="#products"
                            class="inline-flex items-center justify-center rounded-md border border-[#fcefcc] px-4 py-2 text-sm font-medium text-[#fcefcc] transition-colors hover:bg-[#710d17]/20 btn-hover">
                            Explore Digital Products
                        </a>
                    </div>
                </div>
                <div class="relative h-[300px] lg:h-[400px] xl:h-[500px] animate-fade-in" style="animation-delay: 0.3s;">
                    <img src="{{ asset('images/HERMO NO BG.png') }}" alt="Hermosart - Tech talent and innovation"
                        class="h-full w-full object-cover rounded-xl lazy-load" loading="lazy"
                        onload="this.classList.add('lazy-loaded')">
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl text-[#710d17]">Welcome to Hermosart</h2>
                    <p class="max-w-[900px] text-zinc-700 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Hermosart is a tech-focused, AI-powered platform connecting top professionals—web developers, data
                        analysts, AI specialists, and designers—with projects that inspire. Our innovative AI ensures
                        precise
                        talent matching, while our digital product marketplace offers templates, code, and assets to fuel
                        your
                        innovation.
                    </p>
                </div>
            </div>
            <div class="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-4">
                <div class="flex flex-col items-center space-y-2 border border-[#9a2c39]/20 p-6 rounded-lg bg-white shadow-sm card-hover h-full"
                    style="animation-delay: 0.0s;">
                    <div class="p-2 bg-[#fcefcc] rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-6 w-6">
                            <polyline points="16 18 22 12 16 6"></polyline>
                            <polyline points="8 6 2 12 8 18"></polyline>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-center">Web Dev</h3>
                    <p class="text-sm text-center text-zinc-600 flex-grow">
                        Custom websites and web applications built by expert developers.
                    </p>
                </div>
                <div class="flex flex-col items-center space-y-2 border border-[#9a2c39]/20 p-6 rounded-lg bg-white shadow-sm card-hover h-full"
                    style="animation-delay: 0.1s;">
                    <div class="p-2 bg-[#fcefcc] rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-6 w-6">
                            <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                            <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                            <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-center">Data Solutions</h3>
                    <p class="text-sm text-center text-zinc-600 flex-grow">
                        Data scraping, analysis, and visualization by data experts.
                    </p>
                </div>
                <div class="flex flex-col items-center space-y-2 border border-[#9a2c39]/20 p-6 rounded-lg bg-white shadow-sm card-hover h-full"
                    style="animation-delay: 0.2s;">
                    <div class="p-2 bg-[#fcefcc] rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-6 w-6">
                            <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                            <rect x="9" y="9" width="6" height="6"></rect>
                            <line x1="9" y1="1" x2="9" y2="4"></line>
                            <line x1="15" y1="1" x2="15" y2="4"></line>
                            <line x1="9" y1="20" x2="9" y2="23"></line>
                            <line x1="15" y1="20" x2="15" y2="23"></line>
                            <line x1="20" y1="9" x2="23" y2="9"></line>
                            <line x1="20" y1="14" x2="23" y2="14"></line>
                            <line x1="1" y1="9" x2="4" y2="9"></line>
                            <line x1="1" y1="14" x2="4" y2="14"></line>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-center">AI Automation</h3>
                    <p class="text-sm text-center text-zinc-600 flex-grow">
                        Custom AI solutions and chatbots to streamline your business.
                    </p>
                </div>
                <div class="flex flex-col items-center space-y-2 border border-[#9a2c39]/20 p-6 rounded-lg bg-white shadow-sm card-hover h-full"
                    style="animation-delay: 0.3s;">
                    <div class="p-2 bg-[#fcefcc] rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-6 w-6">
                            <circle cx="13.5" cy="6.5" r=".5"></circle>
                            <circle cx="17.5" cy="10.5" r=".5"></circle>
                            <circle cx="8.5" cy="7.5" r=".5"></circle>
                            <circle cx="6.5" cy="12.5" r=".5"></circle>
                            <path
                                d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-center">Design Services</h3>
                    <p class="text-sm text-center text-zinc-600 flex-grow">
                        Brand identity, editorial layouts, and visual design.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl text-[#710d17]">
                        Our Work, Your Inspiration
                    </h2>
                    <p class="max-w-[900px] text-zinc-700 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Explore Hermosart's portfolio of successful projects, powered by our AI-driven talent and technical
                        expertise.
                    </p>
                </div>
            </div>

            <!-- Category Tabs -->
            <div id="portfolio-tabs" class="mx-auto max-w-6xl mt-8 mb-10">
                <div class="bg-gray-100 rounded-lg p-1 grid grid-cols-2 md:grid-cols-4 gap-1">
                    <button data-tab-category="web"
                        class="tab-transition px-2 py-3 text-sm font-medium bg-white focus:outline-none text-center rounded-lg">
                        Web Development
                    </button>
                    <button data-tab-category="data"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        Data Solutions
                    </button>
                    <button data-tab-category="ai"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        AI Automation
                    </button>
                    <button data-tab-category="design"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        Design
                    </button>
                </div>
            </div>

            <div id="portfolio-content" class="mx-auto max-w-6xl py-8">
                <!-- Web Development Tab Content -->
                <div data-tab-content="web" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($portfolioItems as $item)
                            @if ($item['category'] === 'web')
                                <div
                                    class="group relative overflow-hidden rounded-lg bg-white shadow-sm transition-all hover:shadow-lg card-hover">
                                    <div class="aspect-video overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $item['image']) }}" alt="{{ $item['title'] }}"
                                            class="h-full w-full object-cover transition-transform group-hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-[#710d17]">{{ $item['title'] }}</h3>
                                        <p class="mt-2 text-zinc-600">{{ $item['description'] }}</p>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Data Solutions Tab Content -->
                <div data-tab-content="data" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($portfolioItems as $item)
                            @if ($item['category'] === 'data')
                                <div
                                    class="group relative overflow-hidden rounded-lg bg-white shadow-sm transition-all hover:shadow-lg card-hover">
                                    <div class="aspect-video overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $item['image']) }}" alt="{{ $item['title'] }}"
                                            class="h-full w-full object-cover transition-transform group-hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-[#710d17]">{{ $item['title'] }}</h3>
                                        <p class="mt-2 text-zinc-600">{{ $item['description'] }}</p>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- AI Automation Tab Content -->
                <div data-tab-content="ai" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($portfolioItems as $item)
                            @if ($item['category'] === 'ai')
                                <div
                                    class="group relative overflow-hidden rounded-lg bg-white shadow-sm transition-all hover:shadow-lg card-hover">
                                    <div class="aspect-video overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $item['image']) }}" alt="{{ $item['title'] }}"
                                            class="h-full w-full object-cover transition-transform group-hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-[#710d17]">{{ $item['title'] }}</h3>
                                        <p class="mt-2 text-zinc-600">{{ $item['description'] }}</p>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Design Tab Content -->
                <div data-tab-content="design" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($portfolioItems as $item)
                            @if ($item['category'] === 'design')
                                <div
                                    class="group relative overflow-hidden rounded-lg bg-white shadow-sm transition-all hover:shadow-lg card-hover">
                                    <div class="aspect-video overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $item['image']) }}" alt="{{ $item['title'] }}"
                                            class="h-full w-full object-cover transition-transform group-hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-[#710d17]">{{ $item['title'] }}</h3>
                                        <p class="mt-2 text-zinc-600">{{ $item['description'] }}</p>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-12 mb-8">
                <a href="services"
                    class="inline-flex items-center justify-center rounded-md bg-[#710d17] px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-[#9a2c39] btn-hover">
                    View All Services
                </a>
            </div>
        </div>
    </section>

    <!-- Talent Section -->
    <section id="talent" class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl text-[#710d17]">Meet Our Talent</h2>
                    <p class="max-w-[900px] text-zinc-700 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Our AI-vetted professionals bring expertise in web development, data solutions, AI automation, and
                        design.
                        Hire with confidence.
                    </p>
                </div>
            </div>

            <!-- Category Tabs -->
            <div id="talent-tabs" class="mx-auto max-w-6xl mt-8 mb-10">
                <div class="bg-gray-100 rounded-lg p-1 grid grid-cols-2 md:grid-cols-4 gap-1">
                    <button data-tab-category="web"
                        class="tab-transition flex items-center justify-center px-2 sm:px-4 md:px-8 py-3 text-sm font-medium bg-white focus:outline-none text-center rounded-lg">
                        <svg class="w-4 h-4 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <polyline points="16 18 22 12 16 6"></polyline>
                            <polyline points="8 6 2 12 8 18"></polyline>
                        </svg>
                        Web Development
                    </button>
                    <button data-tab-category="data"
                        class="tab-transition flex items-center justify-center px-2 sm:px-4 md:px-8 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        <svg class="w-4 h-4 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                            <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                            <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                        </svg>
                        Data Solutions
                    </button>
                    <button data-tab-category="ai"
                        class="tab-transition flex items-center justify-center px-2 sm:px-4 md:px-8 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        <svg class="w-4 h-4 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                            <rect x="9" y="9" width="6" height="6"></rect>
                            <line x1="9" y1="1" x2="9" y2="4"></line>
                            <line x1="15" y1="1" x2="15" y2="4"></line>
                            <line x1="9" y1="20" x2="9" y2="23"></line>
                            <line x1="15" y1="20" x2="15" y2="23"></line>
                            <line x1="20" y1="9" x2="23" y2="9"></line>
                            <line x1="20" y1="14" x2="23" y2="14"></line>
                            <line x1="1" y1="9" x2="4" y2="9"></line>
                            <line x1="1" y1="14" x2="4" y2="14"></line>
                        </svg>
                        AI Automation
                    </button>
                    <button data-tab-category="design"
                        class="tab-transition flex items-center justify-center px-2 sm:px-4 md:px-8 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        <svg class="w-4 h-4 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <circle cx="13.5" cy="6.5" r=".5"></circle>
                            <circle cx="17.5" cy="10.5" r=".5"></circle>
                            <circle cx="8.5" cy="7.5" r=".5"></circle>
                            <circle cx="6.5" cy="12.5" r=".5"></circle>
                            <path
                                d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z">
                            </path>
                        </svg>
                        Design
                    </button>
                </div>
            </div>

            <div id="talent-content" class="mx-auto max-w-6xl py-8">
                <!-- Web Development Tab Content -->
                <div data-tab-content="web" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-3">
                        @foreach ($professionals as $pro)
                            @if ($pro['category'] === 'web')
                                <div
                                    class="flex flex-col overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover">
                                    <div class="relative bg-gray-100">
                                        <img src="{{ $pro['user']['profile_image'] && Storage::exists('public/' . $pro['user']['profile_image']) ? asset('storage/' . $pro['user']['profile_image']) : 'images/default-profile.jpg' }}"
                                            alt="{{ $pro['name'] }}" class="h-48 w-full object-cover lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                        @if ($pro['is_aiverified'])
                                            <div class="absolute top-3 right-3">
                                                <span
                                                    class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">AI-Verified</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex flex-1 flex-col p-6">
                                        <h3 class="text-lg font-bold text-[#710d17]">{{ $pro['name'] }}</h3>
                                        <p class="text-zinc-600">{{ $pro['title'] }}</p>
                                        <div class="mt-3 flex flex-wrap gap-2">
                                            @foreach ($pro['skills'] as $skill)
                                                <span
                                                    class="inline-flex items-center rounded-md border border-[#9a2c39]/30 px-2 py-1 text-xs font-medium">
                                                    {{ $skill }}
                                                </span>
                                            @endforeach
                                        </div>
                                        <div class="mt-6">
                                            <a href="{{ route('talent.show', $pro['slug'] ?? $pro['id']) }}"
                                                class="block w-full rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc]/50 btn-hover text-center">
                                                View Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Data Solutions Tab Content -->
                <div data-tab-content="data" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-3">
                        @foreach ($professionals as $pro)
                            @if ($pro['category'] === 'data')
                                <div
                                    class="flex flex-col overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover">
                                    <div class="relative bg-gray-100">
                                        <img src="{{ $pro['user']['profile_image'] && Storage::exists('public/' . $pro['user']['profile_image']) ? asset('storage/' . $pro['user']['profile_image']) : 'images/default-profile.jpg' }}"
                                            alt="{{ $pro['name'] }}" class="h-48 w-full object-cover lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                        @if ($pro['is_aiverified'])
                                            <div class="absolute top-3 right-3">
                                                <span
                                                    class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">AI-Verified</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex flex-1 flex-col p-6">
                                        <h3 class="text-lg font-bold text-[#710d17]">{{ $pro['name'] }}</h3>
                                        <p class="text-zinc-600">{{ $pro['title'] }}</p>
                                        <div class="mt-3 flex flex-wrap gap-2">
                                            @foreach ($pro['skills'] as $skill)
                                                <span
                                                    class="inline-flex items-center rounded-md border border-[#9a2c39]/30 px-2 py-1 text-xs font-medium">
                                                    {{ $skill }}
                                                </span>
                                            @endforeach
                                        </div>
                                        <div class="mt-6">
                                            <a href="{{ route('talent.show', $pro['slug'] ?? $pro['id']) }}"
                                                class="block w-full rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc]/50 btn-hover text-center">
                                                View Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- AI Automation Tab Content -->
                <div data-tab-content="ai" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-3">
                        @foreach ($professionals as $pro)
                            @if ($pro['category'] === 'ai')
                                <div
                                    class="flex flex-col overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover">
                                    <div class="relative bg-gray-100">
                                        <img src="{{ $pro['user']['profile_image'] && Storage::exists('public/' . $pro['user']['profile_image']) ? asset('storage/' . $pro['user']['profile_image']) : 'images/default-profile.jpg' }}"
                                            alt="{{ $pro['name'] }}" class="h-48 w-full object-cover lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                        @if ($pro['is_aiverified'])
                                            <div class="absolute top-3 right-3">
                                                <span
                                                    class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">AI-Verified</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex flex-1 flex-col p-6">
                                        <h3 class="text-lg font-bold text-[#710d17]">{{ $pro['name'] }}</h3>
                                        <p class="text-zinc-600">{{ $pro['title'] }}</p>
                                        <div class="mt-3 flex flex-wrap gap-2">
                                            @foreach ($pro['skills'] as $skill)
                                                <span
                                                    class="inline-flex items-center rounded-md border border-[#9a2c39]/30 px-2 py-1 text-xs font-medium">
                                                    {{ $skill }}
                                                </span>
                                            @endforeach
                                        </div>
                                        <div class="mt-6">
                                            <a href="{{ route('talent.show', $pro['slug'] ?? $pro['id']) }}"
                                                class="block w-full rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc]/50 btn-hover text-center">
                                                View Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Design Tab Content -->
                <div data-tab-content="design" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-3">
                        @foreach ($professionals as $pro)
                            @if ($pro['category'] === 'design')
                                <div
                                    class="flex flex-col overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover">
                                    <div class="relative bg-gray-100">
                                        <img src="{{ $pro['user']['profile_image'] && Storage::exists('public/' . $pro['user']['profile_image']) ? asset('storage/' . $pro['user']['profile_image']) : 'images/default-profile.jpg' }}"
                                            alt="{{ $pro['name'] }}" class="h-48 w-full object-cover lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                        @if ($pro['is_aiverified'])
                                            <div class="absolute top-3 right-3">
                                                <span
                                                    class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">AI-Verified</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex flex-1 flex-col p-6">
                                        <h3 class="text-lg font-bold text-[#710d17]">{{ $pro['name'] }}</h3>
                                        <p class="text-zinc-600">{{ $pro['title'] }}</p>
                                        <div class="mt-3 flex flex-wrap gap-2">
                                            @foreach ($pro['skills'] as $skill)
                                                <span
                                                    class="inline-flex items-center rounded-md border border-[#9a2c39]/30 px-2 py-1 text-xs font-medium">
                                                    {{ $skill }}
                                                </span>
                                            @endforeach
                                        </div>
                                        <div class="mt-6">
                                            <a href="{{ route('talent.show', $pro['slug'] ?? $pro['id']) }}"
                                                class="block w-full rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] hover:bg-[#fcefcc]/50 btn-hover text-center">
                                                View Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-12 mb-8">
                <a href="/talent"
                    class="inline-flex items-center justify-center rounded-md bg-[#710d17] px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-[#9a2c39] btn-hover">
                    Hire Talent Now
                </a>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl text-[#710d17]">Shop Digital Products</h2>
                    <p class="max-w-[900px] text-zinc-700 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Boost your projects with our curated collection of web templates, data tools, AI assets, and design
                        resources—created by our talented community.
                    </p>
                </div>
            </div>

            <!-- Category Tabs -->
            <div id="products-tabs" class="mx-auto max-w-6xl mt-8 mb-10">
                <div class="bg-gray-100 rounded-lg p-1 grid grid-cols-2 md:grid-cols-4 gap-1">
                    <button data-tab-category="web"
                        class="tab-transition px-2 py-3 text-sm font-medium bg-white focus:outline-none text-center rounded-lg">
                        Web Templates
                    </button>
                    <button data-tab-category="data"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        Data Tools
                    </button>
                    <button data-tab-category="ai"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        AI Assets
                    </button>
                    <button data-tab-category="design"
                        class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                        Design Resources
                    </button>
                </div>
            </div>

            <div id="products-content" class="mx-auto max-w-6xl py-8">
                <!-- Web Templates Tab Content -->
                <div data-tab-content="web" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($products as $product)
                            @if ($product['category'] === 'web')
                                <div
                                    class="overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover flex flex-col h-full">
                                    <div
                                        class="aspect-video w-full overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $product['image']) }}"
                                            alt="{{ $product['name'] }}"
                                            class="h-full w-full object-cover transition-transform hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6 flex-grow">
                                        <h3 class="line-clamp-1 text-lg font-semibold text-[#710d17]">
                                            {{ $product['name'] }}</h3>
                                        <p class="text-sm text-zinc-500 mt-1">${{ $product['price'] }}</p>
                                    </div>
                                    <div class="px-6 pb-6 pt-0 mt-auto mb-6">
                                        <button
                                            class="w-full rounded-md bg-[#710d17] px-4 py-3 text-sm font-medium text-white hover:bg-[#9a2c39] btn-hover">
                                            Buy Now
                                        </button>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Data Tools Tab Content -->
                <div data-tab-content="data" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($products as $product)
                            @if ($product['category'] === 'data')
                                <div
                                    class="overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover flex flex-col h-full">
                                    <div
                                        class="aspect-video w-full overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $product['image']) }}"
                                            alt="{{ $product['name'] }}"
                                            class="h-full w-full object-cover transition-transform hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6 flex-grow">
                                        <h3 class="line-clamp-1 text-lg font-semibold text-[#710d17]">
                                            {{ $product['name'] }}</h3>
                                        <p class="text-sm text-zinc-500 mt-1">${{ $product['price'] }}</p>
                                    </div>
                                    <div class="px-6 pb-6 pt-0 mt-auto mb-6">
                                        <button
                                            class="w-full rounded-md bg-[#710d17] px-4 py-3 text-sm font-medium text-white hover:bg-[#9a2c39] btn-hover">
                                            Buy Now
                                        </button>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- AI Assets Tab Content -->
                <div data-tab-content="ai" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($products as $product)
                            @if ($product['category'] === 'ai')
                                <div
                                    class="overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover flex flex-col h-full">
                                    <div
                                        class="aspect-video w-full overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $product['image']) }}"
                                            alt="{{ $product['name'] }}"
                                            class="h-full w-full object-cover transition-transform hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6 flex-grow">
                                        <h3 class="line-clamp-1 text-lg font-semibold text-[#710d17]">
                                            {{ $product['name'] }}</h3>
                                        <p class="text-sm text-zinc-500 mt-1">${{ $product['price'] }}</p>
                                    </div>
                                    <div class="px-6 pb-6 pt-0 mt-auto mb-6">
                                        <button
                                            class="w-full rounded-md bg-[#710d17] px-4 py-3 text-sm font-medium text-white hover:bg-[#9a2c39] btn-hover">
                                            Buy Now
                                        </button>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Design Resources Tab Content -->
                <div data-tab-content="design" class="tab-content">
                    <div class="grid gap-8 md:grid-cols-2">
                        @foreach ($products as $product)
                            @if ($product['category'] === 'design')
                                <div
                                    class="overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-lg card-hover flex flex-col h-full">
                                    <div
                                        class="aspect-video w-full overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <img src="{{ asset('storage/' . $product['image']) }}"
                                            alt="{{ $product['name'] }}"
                                            class="h-full w-full object-cover transition-transform hover:scale-105 lazy-load"
                                            loading="lazy" onload="this.classList.add('lazy-loaded')">
                                    </div>
                                    <div class="p-6 flex-grow">
                                        <h3 class="line-clamp-1 text-lg font-semibold text-[#710d17]">
                                            {{ $product['name'] }}</h3>
                                        <p class="text-sm text-zinc-500 mt-1">${{ $product['price'] }}</p>
                                    </div>
                                    <div class="px-6 pb-6 pt-0 mt-auto mb-6">
                                        <button
                                            class="w-full rounded-md bg-[#710d17] px-4 py-3 text-sm font-medium text-white hover:bg-[#9a2c39] btn-hover">
                                            Buy Now
                                        </button>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-12 mb-8">
                <a href="/products"
                    class="inline-flex items-center justify-center rounded-md border border-[#710d17] px-6 py-3 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#fcefcc]/50 btn-hover">
                    Browse All Products
                </a>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-[#fcefcc]/30">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl text-[#710d17]">What Our Community Says</h2>
                </div>
            </div>
            <div class="mx-auto max-w-4xl py-12">
                <div class="grid gap-8 md:grid-cols-3">
                    @foreach ($testimonials as $index => $testimonial)
                        <div class="rounded-lg border-none bg-white p-6 shadow-sm card-hover"
                            style="animation-delay: {{ 0.2 * $index }}s;">
                            <div class="flex items-center gap-4 pb-4">
                                <div class="relative h-12 w-12 overflow-hidden rounded-full">
                                    <img src="{{ $testimonial['avatar'] && Storage::exists('public/' . $testimonial['avatar']) ? asset('storage/' . $testimonial['avatar']) : asset('images/avatar/default/' . rand(1, 10) . '.jpg') }}"
                                        alt="{{ $testimonial['name'] }}" class="h-full w-full object-cover lazy-load"
                                        loading="lazy" onload="this.classList.add('lazy-loaded')">
                                </div>
                                <div>
                                    <p class="text-lg font-semibold">{{ $testimonial['name'] }}</p>
                                    <p class="text-sm text-zinc-500">{{ $testimonial['role'] }}</p>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="h-8 w-8 text-[#9a2c39]/20 mb-2">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <p class="text-lg text-zinc-700">{{ $testimonial['content'] }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl">Join the Hermosart Community</h2>
                    <p class="max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Whether you're hiring talent, seeking projects, or shopping digital products, Hermosart is your tech
                        hub.
                    </p>
                </div>
                <div class="mx-auto grid max-w-5xl gap-6 py-12 md:grid-cols-2 lg:gap-12">
                    <div class="flex flex-col items-center space-y-4 rounded-lg bg-white/10 p-6 backdrop-blur-sm card-hover"
                        style="animation-delay: 0.1s;">
                        <h3 class="text-xl font-bold">For Clients</h3>
                        <p class="text-center">
                            Find the perfect talent for your project with our AI-powered matching system.
                        </p>
                        <a href="/talent"
                            class="inline-flex w-full items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3] btn-hover">
                            Hire Talent
                        </a>
                    </div>
                    <div class="flex flex-col items-center space-y-4 rounded-lg bg-white/10 p-6 backdrop-blur-sm card-hover"
                        style="animation-delay: 0.3s;">
                        <h3 class="text-xl font-bold">For Professionals</h3>
                        <p class="text-center">
                            Join our talent pool and get matched with projects that match your skills and interests.
                        </p>
                        <a href="{{ route('register') }}"
                            class="inline-flex w-full items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3] btn-hover">
                            Join Our Talent Pool
                        </a>
                    </div>
                </div>
                {{-- <div class="w-full max-w-md space-y-2 animate-fade-in" style="animation-delay: 0.5s;">
                    <h3 class="text-xl font-bold">Stay Updated</h3>
                    <p>Subscribe to our newsletter for the latest updates and opportunities.</p>
                    <form class="flex w-full max-w-sm flex-col gap-2 sm:flex-row sm:gap-0">
                        <input type="email" placeholder="Enter your email" class="flex-1 rounded-md border border-white/20 bg-white/10 px-3 py-2 text-sm text-white placeholder:text-white/70 focus:outline-none focus:ring-2 focus:ring-white sm:rounded-r-none">
                        <button type="submit" class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3] sm:rounded-l-none btn-hover">
                            Subscribe
                        </button>
                    </form>
                </div> --}}
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const menuButton = document.querySelector('[aria-controls="mobile-menu"]');
            const mobileMenu = document.getElementById('mobile-menu');

            if (menuButton && mobileMenu) {
                menuButton.addEventListener('click', function() {
                    const expanded = menuButton.getAttribute('aria-expanded') === 'true';
                    menuButton.setAttribute('aria-expanded', !expanded);
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Tab functionality for Portfolio section
            setupTabs('portfolio-tabs', 'portfolio-content');

            // Tab functionality for Talent section
            setupTabs('talent-tabs', 'talent-content');

            // Tab functionality for Products section
            setupTabs('products-tabs', 'products-content');

            // Function to setup tab functionality
            function setupTabs(tabsContainerId, contentContainerId) {
                const tabsContainer = document.getElementById(tabsContainerId);
                const contentContainer = document.getElementById(contentContainerId);

                if (!tabsContainer || !contentContainer) return;

                const tabs = tabsContainer.querySelectorAll('button');
                const contents = contentContainer.querySelectorAll('[data-tab-content]');

                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', () => {
                        // Remove active class from all tabs
                        tabs.forEach(t => {
                            t.classList.remove('active', 'bg-white');
                        });

                        // Add active class to clicked tab
                        tab.classList.add('active', 'bg-white');

                        // Hide all content
                        contents.forEach(content => {
                            content.classList.add('hidden');
                        });

                        // Show corresponding content
                        const tabCategory = tab.getAttribute('data-tab-category');
                        const targetContent = contentContainer.querySelector(
                            `[data-tab-content="${tabCategory}"]`);

                        if (targetContent) {
                            targetContent.classList.remove('hidden');
                        }
                    });
                });

                // Activate first tab by default
                if (tabs.length > 0) {
                    tabs[0].click();
                }
            }

            // Add smooth animations for sections
            const sections = document.querySelectorAll('section');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            sections.forEach(section => {
                section.classList.add('opacity-0', 'transition-opacity', 'duration-1000');
                observer.observe(section);
            });
        });
    </script>
@endpush

@push('styles')
    <style>
        .animate-fade-in {
            opacity: 1;
        }

        .tab-transition {
            transition: all 0.3s ease-in-out;
        }

        .tab-content {
            transition: opacity 0.3s ease-in-out;
        }

        /* Tab styling */
        .bg-gray-100 {
            background-color: #f3f4f6;
        }

        button.tab-transition {
            font-weight: 500;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            button.tab-transition {
                font-size: 0.75rem;
            }
        }

        /* Ensure tabs stack properly on small screens */
        @media (max-width: 767px) {
            .grid-cols-2 button.tab-transition {
                margin-bottom: 0.5rem;
            }
        }

        button.tab-transition.bg-white {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        button.tab-transition:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        button.tab-transition.active {
            background-color: white;
        }

        /* Hover effects for cards */
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Button hover animations */
        .btn-hover {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-hover:after {
            content: '';
            position: absolute;
            width: 0%;
            height: 100%;
            top: 0;
            left: -10%;
            transform: skewX(-15deg);
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
        }

        .btn-hover:hover:after {
            width: 120%;
            left: -10%;
        }

        /* Image loading optimization */
        .lazy-load {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .lazy-loaded {
            opacity: 1;
        }
    </style>
@endpush
