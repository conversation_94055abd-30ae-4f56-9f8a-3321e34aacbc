<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Earning;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SellerDashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('seller');
    }

    /**
     * Show the seller dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();

        // Get real data from database
        $totalProducts = Product::where('user_id', $user->id)->count();
        
        $totalOrders = Order::whereHas('product', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->count();

        $totalEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Paid')
            ->sum('amount');

        $stats = [
            'total_products' => $totalProducts,
            'total_orders' => $totalOrders,
            'total_earnings' => $totalEarnings
        ];

        // Get recent products
        $recentProducts = Product::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent orders
        $recentOrders = Order::whereHas('product', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->with(['user', 'product'])
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();

        return view('dashboard.seller.index', compact('user', 'stats', 'recentProducts', 'recentOrders'));
    }

    /**
     * Show the seller products page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function products()
    {
        $user = Auth::user();

        $products = Product::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard.seller.products', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function createProduct()
    {
        $categories = \App\Models\Category::all();
        return view('dashboard.seller.create-product', compact('categories'));
    }

    /**
     * Store a new product.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeProduct(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'category' => 'required|string|exists:categories,id',
            'image' => 'nullable|image|max:2048',
        ]);

        $slug = Str::slug($validated['name']);

        $product = new Product();
        $product->user_id = $user->id;
        $product->name = $validated['name'];
        $product->slug = $slug;
        $product->price = $validated['price'];
        $product->description = $validated['description'];
        $product->category = $validated['category'];
        $product->rating = 0;
        $product->sales = 0;

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        $product->save();

        return redirect()->route('dashboard.seller.products')->with('success', 'Product added successfully');
    }

    /**
     * Show the seller earnings page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function earnings(Request $request)
    {
        $user = Auth::user();
        $status = $request->query('status');

        $query = Earning::where('professional_id', $user->id)
            ->with(['order'])
            ->orderBy('created_at', 'desc');

        // Apply status filter if provided and not 'All Earnings'
        if ($status && $status !== 'All Earnings') {
            $query->where('status', $status);
        }

        $earnings = $query->get();

        $totalEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Paid')
            ->sum('amount');

        $pendingEarnings = Earning::where('professional_id', $user->id)
            ->where('status', 'Pending')
            ->sum('amount');

        // Get all possible statuses for the filter dropdown
        $statuses = ['All Earnings', 'Paid', 'Pending', 'Cancelled'];

        return view('dashboard.seller.earnings', compact('earnings', 'totalEarnings', 'pendingEarnings', 'statuses', 'status'));
    }

    /**
     * Show the seller profile page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = Auth::user();
        return view('dashboard.seller.profile', compact('user'));
    }
}
