<?php

namespace Database\Seeders;

use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SellerApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get seller users
        $proSeller = User::where('email', '<EMAIL>')->first();
        $seller = User::where('email', '<EMAIL>')->first();
        
        if (!$proSeller || !$seller) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }
        
        // Create seller application for pro-seller
        if (!SellerApplication::where('user_id', $proSeller->id)->exists()) {
            SellerApplication::create([
                'user_id' => $proSeller->id,
                'business_name' => 'Web Solutions Plus',
                'business_name_slug' => 'web-solutions-plus',
                'business_description' => 'We provide high-quality web development solutions and digital products for businesses of all sizes. Our team specializes in responsive design, e-commerce solutions, and custom web applications.',
                'phone_number' => '************',
                'paypal_email' => '<EMAIL>',
                'status' => 'approved',
                'approved_at' => Carbon::now()->subMonths(6),
                'created_at' => Carbon::now()->subMonths(6)->subDays(3),
            ]);
        }
        
        // Create seller application for seller
        if (!SellerApplication::where('user_id', $seller->id)->exists()) {
            SellerApplication::create([
                'user_id' => $seller->id,
                'business_name' => 'Digital Products Co.',
                'business_name_slug' => 'digital-products-co',
                'business_description' => 'Digital Products Co. offers premium digital assets, templates, and tools for designers, developers, and content creators. Our products are designed to save you time and enhance your projects.',
                'phone_number' => '************',
                'paypal_email' => '<EMAIL>',
                'status' => 'approved',
                'approved_at' => Carbon::now()->subMonths(3),
                'created_at' => Carbon::now()->subMonths(3)->subDays(5),
            ]);
        }
        
        // Create additional seller applications for variety
        $additionalSellers = [
            [
                'name' => 'Emma Davis',
                'email' => '<EMAIL>',
                'business_name' => 'Creative Templates',
                'business_description' => 'Premium templates for presentations, websites, and marketing materials.',
            ],
            [
                'name' => 'James Wilson',
                'email' => '<EMAIL>',
                'business_name' => 'Code Snippets Pro',
                'business_description' => 'Time-saving code snippets and components for developers.',
            ],
            [
                'name' => 'Ava Brown',
                'email' => '<EMAIL>',
                'business_name' => 'Digital Art Studio',
                'business_description' => 'High-quality digital art assets and illustrations for creative projects.',
            ],
        ];
        
        foreach ($additionalSellers as $sellerData) {
            // Check if user exists, create if not
            $user = User::where('email', $sellerData['email'])->first();
            
            if (!$user) {
                $user = User::create([
                    'name' => $sellerData['name'],
                    'email' => $sellerData['email'],
                    'email_verified_at' => now(),
                    'password' => bcrypt('password'),
                    'account_type' => 'seller',
                    'is_seller' => true,
                ]);
            }
            
            // Create seller application if not exists
            if (!SellerApplication::where('user_id', $user->id)->exists()) {
                SellerApplication::create([
                    'user_id' => $user->id,
                    'business_name' => $sellerData['business_name'],
                    'business_name_slug' => Str::slug($sellerData['business_name']),
                    'business_description' => $sellerData['business_description'],
                    'phone_number' => '555-' . rand(100, 999) . '-' . rand(1000, 9999),
                    'paypal_email' => strtolower(str_replace(' ', '.', $sellerData['name'])) . '@example.com',
                    'status' => 'approved',
                    'approved_at' => Carbon::now()->subMonths(rand(1, 12)),
                    'created_at' => Carbon::now()->subMonths(rand(1, 12))->subDays(rand(1, 10)),
                ]);
            }
        }
    }
}
