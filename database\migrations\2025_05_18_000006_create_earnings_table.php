<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('earnings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('professional_id');
            $table->uuid('project_id')->nullable();
            $table->uuid('order_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('description');
            $table->enum('status', ['Pending', 'Paid', 'Cancelled']);
            $table->date('payment_date')->nullable();
            $table->string('transaction_id')->nullable();
            $table->timestamps();

            $table->foreign('professional_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('set null');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('earnings');
    }
};
