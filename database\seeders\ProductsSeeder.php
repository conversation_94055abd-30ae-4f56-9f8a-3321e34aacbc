<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use App\Models\User;
use App\Models\Professional;
use Illuminate\Database\Seeder;

class ProductsSeeder extends Seeder
{
    public function run(): void
    {
        // Get professional users
        $professionalUser = User::where('email', '<EMAIL>')->first();
        $proSellerUser = User::where('email', '<EMAIL>')->first();

        if (!$professionalUser || !$proSellerUser) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }

        // Get professional records
        $professional = Professional::where('user_id', $professionalUser->id)->first();
        $proSeller = Professional::where('user_id', $proSellerUser->id)->first();

        if (!$professional || !$proSeller) {
            // Create professional records if they don't exist
            if (!$professional) {
                $professional = new Professional();
                $professional->id = (string) \Illuminate\Support\Str::uuid();
                $professional->user_id = $professionalUser->id;
                $professional->name = $professionalUser->name;
                $professional->title = 'Web Developer';
                $professional->skills = ['HTML', 'CSS', 'JavaScript'];
                $professional->image = 'professionals/default.jpg';
                $professional->description = 'Professional web developer';
                $professional->experience = '5 years';
                $professional->rating = 4.8;
                $professional->projects = 0;
                $professional->category = 'web';
                $professional->is_aiverified = true;
                $professional->save();
            }

            if (!$proSeller) {
                $proSeller = new Professional();
                $proSeller->id = (string) \Illuminate\Support\Str::uuid();
                $proSeller->user_id = $proSellerUser->id;
                $proSeller->name = $proSellerUser->name;
                $proSeller->title = 'Data Scientist';
                $proSeller->skills = ['Python', 'R', 'Machine Learning'];
                $proSeller->image = 'professionals/default.jpg';
                $proSeller->description = 'Professional data scientist';
                $proSeller->experience = '7 years';
                $proSeller->rating = 4.9;
                $proSeller->projects = 0;
                $proSeller->category = 'data';
                $proSeller->is_aiverified = true;
                $proSeller->save();
            }
        }

        // Products
        $products = [
            'web' => [
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Responsive Website Template',
                    'price' => 49,
                    'image' => 'images/product-1.jpg',
                    'description' => 'Modern, responsive website template for businesses and portfolios.',
                    'rating' => 4.8,
                    'sales' => 235,
                    'category' => 'web',
                ],
                [
                    'id' => '550e8400-e29b-41d4-a716-************', // Featured
                    'name' => 'Admin Dashboard UI Kit',
                    'price' => 79,
                    'image' => 'images/product-2.jpg',
                    'description' => 'Complete admin panel with charts, tables, and user management.',
                    'rating' => 4.9,
                    'sales' => 189,
                    'category' => 'web',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'E-commerce Template',
                    'price' => 99,
                    'image' => 'images/product-3.jpg',
                    'description' => 'Full-featured online store template with product pages and checkout.',
                    'rating' => 4.7,
                    'sales' => 156,
                    'category' => 'web',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Landing Page Bundle',
                    'price' => 59,
                    'image' => 'images/product-4.jpg',
                    'description' => 'Collection of high-converting landing page templates for various industries.',
                    'rating' => 4.6,
                    'sales' => 210,
                    'category' => 'web',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Portfolio Template',
                    'price' => 39,
                    'image' => 'images/product-5.jpg',
                    'description' => 'Showcase your work with this elegant portfolio template.',
                    'rating' => 4.8,
                    'sales' => 178,
                    'category' => 'web',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Blog Theme',
                    'price' => 49,
                    'image' => 'images/product-6.jpg',
                    'description' => 'Clean and modern blog theme with multiple layout options.',
                    'rating' => 4.7,
                    'sales' => 145,
                    'category' => 'web',
                ],
            ],
            'data' => [
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Data Visualization Package',
                    'price' => 39,
                    'image' => 'images/product-7.jpg',
                    'description' => 'Ready-to-use charts, graphs, and visualization components.',
                    'rating' => 4.8,
                    'sales' => 167,
                    'category' => 'data',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Analytics Dashboard Template',
                    'price' => 59,
                    'image' => 'images/product-8.jpg',
                    'description' => 'Customizable dashboard for displaying business metrics and KPIs.',
                    'rating' => 4.9,
                    'sales' => 142,
                    'category' => 'data',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Data Processing Scripts',
                    'price' => 29,
                    'image' => 'images/product-9.jpg',
                    'description' => 'Collection of Python scripts for data cleaning and transformation.',
                    'rating' => 4.6,
                    'sales' => 123,
                    'category' => 'data',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Business Intelligence Templates',
                    'price' => 69,
                    'image' => 'images/product-10.jpg',
                    'description' => 'Ready-made BI reports and dashboards for common business needs.',
                    'rating' => 4.7,
                    'sales' => 98,
                    'category' => 'data',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Data Mining Toolkit',
                    'price' => 49,
                    'image' => 'images/product-11.jpg',
                    'description' => 'Tools and algorithms for extracting patterns from large datasets.',
                    'rating' => 4.8,
                    'sales' => 87,
                    'category' => 'data',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Excel Dashboard Templates',
                    'price' => 19,
                    'image' => 'images/product-12.jpg',
                    'description' => 'Professional Excel dashboards for business reporting.',
                    'rating' => 4.5,
                    'sales' => 215,
                    'category' => 'data',
                ],
            ],
            'ai' => [
                [
                    'id' => '550e8400-e29b-41d4-a716-************', // Featured
                    'name' => 'Chatbot Starter Kit',
                    'price' => 99,
                    'image' => 'images/product-13.jpg',
                    'description' => 'Complete framework for building custom AI chatbots for your business.',
                    'rating' => 4.9,
                    'sales' => 132,
                    'category' => 'ai',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'AI Integration Boilerplate',
                    'price' => 129,
                    'image' => 'images/product-14.jpg',
                    'description' => 'Code templates for integrating AI services into your applications.',
                    'rating' => 4.8,
                    'sales' => 98,
                    'category' => 'ai',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'NLP Model Templates',
                    'price' => 149,
                    'image' => 'images/product-15.jpg',
                    'description' => 'Pre-trained natural language processing models for common use cases.',
                    'rating' => 4.7,
                    'sales' => 76,
                    'category' => 'ai',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'AI-Powered Form Processing',
                    'price' => 79,
                    'image' => 'images/product-16.jpg',
                    'description' => 'Automated form data extraction and processing solution.',
                    'rating' => 4.6,
                    'sales' => 105,
                    'category' => 'ai',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Computer Vision Toolkit',
                    'price' => 119,
                    'image' => 'images/product-17.jpg',
                    'description' => 'Tools and models for image recognition and processing.',
                    'rating' => 4.8,
                    'sales' => 89,
                    'category' => 'ai',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'AI Content Generator',
                    'price' => 69,
                    'image' => 'images/product-18.jpg',
                    'description' => 'Generate blog posts, product descriptions, and marketing copy.',
                    'rating' => 4.7,
                    'sales' => 143,
                    'category' => 'ai',
                ],
            ],
            'design' => [
                [
                    'id' => '550e8400-e29b-41d4-a716-************', // Featured
                    'name' => 'Brand Identity Package',
                    'price' => 149,
                    'image' => 'images/product-19.jpg',
                    'description' => 'Complete brand identity kit with logo templates and guidelines.',
                    'rating' => 4.9,
                    'sales' => 176,
                    'category' => 'design',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Editorial Layout Templates',
                    'price' => 69,
                    'image' => 'images/product-20.jpg',
                    'description' => 'Professional templates for magazines, brochures, and reports.',
                    'rating' => 4.8,
                    'sales' => 154,
                    'category' => 'design',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'UI Component Library',
                    'price' => 89,
                    'image' => 'images/product-21.jpg',
                    'description' => 'Extensive collection of UI components for web and mobile apps.',
                    'rating' => 4.7,
                    'sales' => 132,
                    'category' => 'design',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Social Media Kit',
                    'price' => 49,
                    'image' => 'images/product-22.jpg',
                    'description' => 'Templates for social media posts, stories, and ads.',
                    'rating' => 4.6,
                    'sales' => 198,
                    'category' => 'design',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Icon Pack',
                    'price' => 29,
                    'image' => 'images/product-23.jpg',
                    'description' => 'Versatile icon set for web and mobile applications.',
                    'rating' => 4.8,
                    'sales' => 245,
                    'category' => 'design',
                ],
                [
                    'id' => (string) \Illuminate\Support\Str::uuid(),
                    'name' => 'Presentation Templates',
                    'price' => 39,
                    'image' => 'images/product-24.jpg',
                    'description' => 'Professional PowerPoint and Keynote presentation templates.',
                    'rating' => 4.7,
                    'sales' => 187,
                    'category' => 'design',
                ],
            ],
        ];

        foreach ($products as $category => $categoryProducts) {
            foreach ($categoryProducts as $product) {
                // Assign professional_id based on category
                if ($category == 'web' || $category == 'design') {
                    $product['professional_id'] = $professional->id;
                    $product['user_id'] = $professionalUser->id;
                } else {
                    $product['professional_id'] = $proSeller->id;
                    $product['user_id'] = $proSellerUser->id;
                }

                Product::create($product);
            }
        }
    }
}