<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PaymentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a category for testing
        Category::create([
            'id' => 'web',
            'name' => 'Web Development',
            'description' => 'Web development products',
            'icon' => 'web-icon'
        ]);
    }

    public function test_user_can_access_payment_initiation_page()
    {
        // Create a user and product
        $user = User::factory()->create();
        $seller = User::factory()->create(['is_seller' => true]);

        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 29.99,
            'image' => 'test-image.jpg',
            'description' => 'Test product description',
            'rating' => 4.5,
            'sales' => 10,
            'category' => 'web',
            'user_id' => $seller->id
        ]);

        // Test that authenticated user can access product detail page
        $response = $this->actingAs($user)
            ->get(route('products.show', $product->id));

        $response->assertStatus(200);
        $response->assertSee('Buy Now');
    }

    public function test_user_cannot_buy_own_product()
    {
        // Create a seller user
        $seller = User::factory()->create(['is_seller' => true]);
        
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 29.99,
            'image' => 'test-image.jpg',
            'description' => 'Test product description',
            'rating' => 4.5,
            'sales' => 10,
            'category' => 'web',
            'user_id' => $seller->id
        ]);

        // Test that seller cannot see buy button for their own product
        $response = $this->actingAs($seller)
            ->get(route('products.show', $product->id));

        $response->assertStatus(200);
        $response->assertSee('This is your product');
        $response->assertDontSee('Buy Now');
    }

    public function test_guest_user_redirected_to_login()
    {
        // Create a product
        $seller = User::factory()->create(['is_seller' => true]);
        
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 29.99,
            'image' => 'test-image.jpg',
            'description' => 'Test product description',
            'rating' => 4.5,
            'sales' => 10,
            'category' => 'web',
            'user_id' => $seller->id
        ]);

        // Test that guest user sees login link
        $response = $this->get(route('products.show', $product->id));

        $response->assertStatus(200);
        $response->assertSee('Login to Buy');
    }

    public function test_payment_initiation_requires_authentication()
    {
        // Create a product
        $seller = User::factory()->create(['is_seller' => true]);
        
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 29.99,
            'image' => 'test-image.jpg',
            'description' => 'Test product description',
            'rating' => 4.5,
            'sales' => 10,
            'category' => 'web',
            'user_id' => $seller->id
        ]);

        // Test that unauthenticated user is redirected to login
        $response = $this->post(route('payment.initiate', $product->id));

        $response->assertRedirect(route('login'));
    }

    public function test_order_creation_on_payment_initiation()
    {
        // This test would require mocking the PayPal service
        // For now, we'll just test that the route exists and requires auth
        
        $user = User::factory()->create();
        $seller = User::factory()->create(['is_seller' => true]);
        
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 29.99,
            'image' => 'test-image.jpg',
            'description' => 'Test product description',
            'rating' => 4.5,
            'sales' => 10,
            'category' => 'web',
            'user_id' => $seller->id
        ]);

        // Test that authenticated user can access payment initiation
        // Note: This will fail in testing environment without proper PayPal credentials
        // but it tests the route and authentication
        $response = $this->actingAs($user)
            ->post(route('payment.initiate', $product->id));

        // Should either redirect to PayPal or show an error
        $this->assertTrue(
            $response->isRedirection() || 
            $response->getStatusCode() === 302
        );
    }
}
