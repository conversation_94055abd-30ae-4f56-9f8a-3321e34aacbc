<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Earning extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'professional_id', 'project_id', 'order_id', 'amount', 
        'description', 'status', 'payment_date', 'transaction_id'
    ];

    protected $casts = [
        'amount' => 'float',
        'payment_date' => 'date',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    public function professional()
    {
        return $this->belongsTo(User::class, 'professional_id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
