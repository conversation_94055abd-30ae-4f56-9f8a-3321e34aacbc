<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="{{ route('dashboard.professional') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
    <a href="{{ route('dashboard.professional.projects') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
    <a href="{{ route('dashboard.professional.messages') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
    <a href="{{ route('dashboard.professional.portfolio') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.portfolio*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Portfolio</a>
    <a href="{{ route('dashboard.professional.products') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.products*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Products</a>
    <a href="{{ route('dashboard.professional.services') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.services*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Services</a>
    <a href="{{ route('dashboard.professional.earnings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Earnings</a>
    <a href="{{ route('dashboard.professional.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
    <a href="{{ route('dashboard.professional.settings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
    <a href="{{ route('home') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
