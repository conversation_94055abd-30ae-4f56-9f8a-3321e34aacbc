@extends('layouts.app')

@section('title', 'Stores - Hermosart')

@section('content')
    <!-- Hero Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h1 class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc]">
                        Our Seller Stores
                    </h1>
                    <p class="max-w-[600px] text-zinc-200 md:text-xl">
                        Discover unique digital products from our independent sellers
                    </p>
                </div>
                <div class="w-full max-w-sm space-y-2">
                    <form class="flex space-x-2">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 flex-1"
                            placeholder="Search stores..." type="search" />
                        <button
                            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-[#fcefcc] text-[#710d17] hover:bg-[#fcefcc]/90 h-10 px-4 py-2">
                            Search
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Stores Section -->
    <section class="w-full py-12 md:py-24 lg:py-32">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                        Browse Our Stores
                    </h2>
                    <p class="max-w-[900px] text-zinc-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Discover unique digital products from our talented sellers
                    </p>
                </div>
            </div>

            <div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                @forelse($sellers as $seller)
                    @php
                        $sellerApplication = $seller->sellerApplications()->where('status', 'approved')->first();
                        if (!$sellerApplication) {
                            continue;
                        }
                    @endphp
                    <div
                        class="flex flex-col overflow-hidden rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl">
                        <div class="h-48 bg-gray-100 relative">
                            @if ($seller->store_image && Storage::exists('public/' . $seller->store_image))
                                <img alt="{{ $sellerApplication->business_name }}" class="h-full w-full object-cover"
                                    src="{{ asset('storage/' . $seller->store_image) }}" />
                            @else
                                <div class="flex items-center justify-center h-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="flex flex-1 flex-col p-6">
                            <h3 class="text-xl font-bold text-[#710d17]">{{ $sellerApplication->business_name }}</h3>
                            <p class="mt-2 line-clamp-3 text-sm/relaxed text-gray-500">
                                {{ Str::limit($sellerApplication->business_description, 150) }}
                            </p>
                            <div class="mt-6 flex flex-col gap-2 sm:flex-row sm:items-center">
                                <a href="{{ route('store.show', $sellerApplication->business_name_slug) }}"
                                    class="inline-flex h-10 items-center justify-center rounded-md bg-[#710d17] px-8 text-sm font-medium text-white shadow transition-colors hover:bg-[#9a2c39] focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#710d17] disabled:pointer-events-none disabled:opacity-50">
                                    Visit Store
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No stores found</h3>
                        <p class="mt-1 text-sm text-gray-500">We don't have any seller stores yet.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
@endsection
