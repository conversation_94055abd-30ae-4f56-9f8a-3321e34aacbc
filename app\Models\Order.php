<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Order extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id', 'product_id', 'service_id', 'order_type', 'price', 'status', 'transaction_id',
        'payment_method', 'paypal_payment_id', 'paypal_payer_id', 'paypal_payment_details',
        'completed_at', 'cancellation_reason', 'auto_complete_date'
    ];

    protected $casts = [
        'price' => 'float',
        'paypal_payment_details' => 'array',
        'completed_at' => 'datetime',
        'auto_complete_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the item being ordered (product or service)
     */
    public function getOrderedItem()
    {
        return $this->order_type === 'service' ? $this->service : $this->product;
    }

    /**
     * Check if order can be completed by client
     */
    public function canBeCompleted()
    {
        return $this->status === 'Processing' && $this->order_type === 'service';
    }

    /**
     * Check if order can be cancelled by client
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['Processing']) && $this->order_type === 'service';
    }

    /**
     * Check if order should be auto-completed
     */
    public function shouldAutoComplete()
    {
        return $this->status === 'Processing'
            && $this->auto_complete_date
            && now()->gte($this->auto_complete_date);
    }
}
