

<?php $__env->startSection('title', 'Register - Hermosart'); ?>

<?php $__env->startSection('content'); ?>
<div class="flex min-h-screen flex-col">
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="w-full max-w-md">
            <div class="text-center mb-8">
                <div class="mx-auto h-12 w-12 overflow-hidden rounded-full bg-[#710d17] flex items-center justify-center">
                    <span class="text-[#fcefcc] font-bold text-xl">H</span>
                </div>
                <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900">Create your account</h2>
                <p class="mt-2 text-sm text-gray-600">
                    Already have an account?
                    <a href="<?php echo e(route('login')); ?>" class="font-medium text-[#710d17] hover:text-[#9a2c39]">
                        Sign in
                    </a>
                </p>
            </div>
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <?php if($errors->any()): ?>
                    <div class="rounded-md bg-red-50 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    There were errors with your submission
                                </h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <form class="space-y-6" action="<?php echo e(route('register')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Account Type</label>
                        <div class="mt-2 grid grid-cols-2 gap-3">
                            <div>
                                <input type="radio" name="account_type" id="client" value="client" class="sr-only" checked>
                                <label for="client" class="account-type-label relative block cursor-pointer rounded-lg border-2 border-gray-300 bg-white px-6 py-4 shadow-sm focus:outline-none sm:flex sm:justify-between transition-all duration-200 hover:border-[#710d17]">
                                    <div class="flex items-center">
                                        <div class="text-sm">
                                            <p class="font-medium text-gray-900">Client</p>
                                            <p class="text-gray-500">Hire talent & buy products</p>
                                        </div>
                                    </div>
                                    <div class="mt-2 flex text-sm sm:ml-4 sm:mt-0 sm:flex-col sm:text-right">
                                        <div class="flex items-center">
                                            <div class="rounded-full bg-[#fcefcc] p-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                                    <circle cx="9" cy="7" r="4"></circle>
                                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                            <div>
                                <input type="radio" name="account_type" id="professional" value="professional" class="sr-only">
                                <label for="professional" class="account-type-label relative block cursor-pointer rounded-lg border-2 border-gray-300 bg-white px-6 py-4 shadow-sm focus:outline-none sm:flex sm:justify-between transition-all duration-200 hover:border-[#710d17]">
                                    <div class="flex items-center">
                                        <div class="text-sm">
                                            <p class="font-medium text-gray-900">Professional</p>
                                            <p class="text-gray-500">Offer services & sell products</p>
                                        </div>
                                    </div>
                                    <div class="mt-2 flex text-sm sm:ml-4 sm:mt-0 sm:flex-col sm:text-right">
                                        <div class="flex items-center">
                                            <div class="rounded-full bg-[#fcefcc] p-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <rect width="20" height="14" x="2" y="7" rx="2" ry="2"></rect>
                                                    <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700">
                                First name
                            </label>
                            <div class="mt-1">
                                <input type="text" name="first_name" id="first_name" autocomplete="given-name" value="<?php echo e(old('first_name')); ?>" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                            </div>
                        </div>

                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700">
                                Last name
                            </label>
                            <div class="mt-1">
                                <input type="text" name="last_name" id="last_name" autocomplete="family-name" value="<?php echo e(old('last_name')); ?>" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">
                            Email address
                        </label>
                        <div class="mt-1">
                            <input id="email" name="email" type="email" autocomplete="email" value="<?php echo e(old('email')); ?>" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                        </div>
                    </div>

                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-700">
                            Company (optional)
                        </label>
                        <div class="mt-1">
                            <input type="text" name="company" id="company" value="<?php echo e(old('company')); ?>" class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                        </div>
                    </div>

                    <div id="specialty-field" class="hidden">
                        <label for="specialty" class="block text-sm font-medium text-gray-700">
                            Primary specialty
                        </label>
                        <div class="mt-1">
                            <select id="specialty" name="specialty" class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                                <option value="">Select a specialty</option>
                                <option value="web-development" <?php echo e(old('specialty') == 'web-development' ? 'selected' : ''); ?>>Web Development</option>
                                <option value="mobile-development" <?php echo e(old('specialty') == 'mobile-development' ? 'selected' : ''); ?>>Mobile Development</option>
                                <option value="data-solutions" <?php echo e(old('specialty') == 'data-solutions' ? 'selected' : ''); ?>>Data Solutions</option>
                                <option value="ai-automation" <?php echo e(old('specialty') == 'ai-automation' ? 'selected' : ''); ?>>AI Automation</option>
                                <option value="design" <?php echo e(old('specialty') == 'design' ? 'selected' : ''); ?>>Design</option>
                                <option value="digital-marketing" <?php echo e(old('specialty') == 'digital-marketing' ? 'selected' : ''); ?>>Digital Marketing</option>
                                <option value="content-creation" <?php echo e(old('specialty') == 'content-creation' ? 'selected' : ''); ?>>Content Creation</option>
                                <option value="business-consulting" <?php echo e(old('specialty') == 'business-consulting' ? 'selected' : ''); ?>>Business Consulting</option>
                                <option value="photography" <?php echo e(old('specialty') == 'photography' ? 'selected' : ''); ?>>Photography</option>
                                <option value="video-production" <?php echo e(old('specialty') == 'video-production' ? 'selected' : ''); ?>>Video Production</option>
                                <option value="writing-translation" <?php echo e(old('specialty') == 'writing-translation' ? 'selected' : ''); ?>>Writing & Translation</option>
                                <option value="other" <?php echo e(old('specialty') == 'other' ? 'selected' : ''); ?>>Other</option>
                            </select>
                        </div>
                        <div id="other-specialty-field" class="mt-3 hidden">
                            <label for="other_specialty" class="block text-sm font-medium text-gray-700">
                                Please specify your specialty
                            </label>
                            <div class="mt-1">
                                <input type="text" name="other_specialty" id="other_specialty" value="<?php echo e(old('other_specialty')); ?>" placeholder="Enter your specialty" class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <div class="mt-1">
                            <input id="password" name="password" type="password" autocomplete="new-password" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                        </div>
                    </div>

                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                            Confirm password
                        </label>
                        <div class="mt-1">
                            <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#710d17] focus:border-[#710d17] sm:text-sm">
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input id="terms" name="terms" type="checkbox" required class="h-4 w-4 text-[#710d17] focus:ring-[#710d17] border-gray-300 rounded">
                        <label for="terms" class="ml-2 block text-sm text-gray-900">
                            I agree to the
                            <a href="<?php echo e(route('legal.terms')); ?>" target="_blank" class="text-[#710d17] hover:text-[#9a2c39] underline">terms of service</a>
                            and
                            <a href="<?php echo e(route('legal.privacy')); ?>" target="_blank" class="text-[#710d17] hover:text-[#9a2c39] underline">privacy policy</a>
                        </label>
                    </div>

                    <div>
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                            Create account
                        </button>
                    </div>
                </form>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">
                                Or continue with
                            </span>
                        </div>
                    </div>

                    <div class="mt-6">
                        <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                            </svg>
                            Continue with Google
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clientRadio = document.getElementById('client');
        const professionalRadio = document.getElementById('professional');
        const specialtyField = document.getElementById('specialty-field');
        const specialtySelect = document.getElementById('specialty');
        const otherSpecialtyField = document.getElementById('other-specialty-field');

        // Account type selection highlighting
        function updateAccountTypeSelection() {
            const clientLabel = document.querySelector('label[for="client"]');
            const professionalLabel = document.querySelector('label[for="professional"]');

            // Reset all labels
            clientLabel.classList.remove('border-red-500', 'bg-red-50');
            professionalLabel.classList.remove('border-red-500', 'bg-red-50');

            // Highlight selected option
            if (clientRadio.checked) {
                clientLabel.classList.add('border-red-500', 'bg-red-50');
            } else if (professionalRadio.checked) {
                professionalLabel.classList.add('border-red-500', 'bg-red-50');
            }
        }

        function toggleSpecialtyField() {
            if (professionalRadio.checked) {
                specialtyField.classList.remove('hidden');
            } else {
                specialtyField.classList.add('hidden');
            }
        }

        function toggleOtherSpecialtyField() {
            if (specialtySelect.value === 'other') {
                otherSpecialtyField.classList.remove('hidden');
            } else {
                otherSpecialtyField.classList.add('hidden');
            }
        }

        // Event listeners
        clientRadio.addEventListener('change', function() {
            toggleSpecialtyField();
            updateAccountTypeSelection();
        });

        professionalRadio.addEventListener('change', function() {
            toggleSpecialtyField();
            updateAccountTypeSelection();
        });

        specialtySelect.addEventListener('change', toggleOtherSpecialtyField);

        // Initialize on page load
        toggleSpecialtyField();
        updateAccountTypeSelection();
        toggleOtherSpecialtyField();
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\hermosart\resources\views/auth/register.blade.php ENDPATH**/ ?>