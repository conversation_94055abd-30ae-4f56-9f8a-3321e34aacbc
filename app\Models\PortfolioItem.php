<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PortfolioItem extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'professional_id', 'title', 'description', 'image',
        'category', 'client_name', 'completion_date', 'featured'
    ];

    protected $casts = [
        'completion_date' => 'date',
        'featured' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    public function professional()
    {
        return $this->belongsTo(User::class, 'professional_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category', 'id');
    }
}