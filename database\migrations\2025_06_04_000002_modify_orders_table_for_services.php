<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Make product_id nullable and add service_id
            $table->uuid('product_id')->nullable()->change();
            $table->uuid('service_id')->nullable()->after('product_id');
            $table->enum('order_type', ['product', 'service'])->default('product')->after('service_id');
            
            // Add foreign key for service_id
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['service_id']);
            $table->dropColumn(['service_id', 'order_type']);
            $table->uuid('product_id')->nullable(false)->change();
        });
    }
};
