<?php

namespace App\Http\Controllers;

use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ApplySellerController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the seller application form.
     *
     * @return \Illuminate\View\View
     */
    public function showApplicationForm()
    {
        $user = Auth::user();

        // Check if user already has a pending or approved application
        $existingApplication = SellerApplication::where('user_id', $user->id)
            ->whereIn('status', ['pending', 'approved'])
            ->first();

        if ($existingApplication) {
            return redirect()->route('apply.seller.status')
                ->with('info', 'You already have an application in progress.');
        }

        // Check if user is already a seller
        if ($user->is_seller) {
            return redirect()->route('home')
                ->with('info', 'You are already registered as a seller.');
        }

        return view('apply.seller.form', compact('user'));
    }

    /**
     * Process the seller application submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitApplication(Request $request)
    {
        $user = Auth::user();

        // Validate the form data
        $validator = Validator::make($request->all(), [
            'business_name' => 'required|string|max:255',
            'business_description' => 'required|string|max:1000',
            'phone_number' => 'required|string|max:20',
            'paypal_email' => 'required|email|max:255',
            'store_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle store image upload
        $storeImagePath = null;
        if ($request->hasFile('store_image')) {
            $storeImagePath = $request->file('store_image')->store('user/store_images', 'public');

            // Update user's store_image field
            $user->store_image = $storeImagePath;
            $user->save();
        }

        // Create the application
        $application = new SellerApplication([
            'user_id' => $user->id,
            'business_name' => $request->business_name,
            'business_name_slug' => \Illuminate\Support\Str::slug($request->business_name),
            'business_description' => $request->business_description,
            'phone_number' => $request->phone_number,
            'paypal_email' => $request->paypal_email,
            'status' => 'pending'
        ]);

        $application->save();

        return redirect()->route('dashboard.seller')
            ->with('success', 'Your seller application has been submitted successfully.');
    }

    /**
     * Show the application status page.
     *
     * @return \Illuminate\View\View
     */
    public function showApplicationStatus()
    {
        $user = Auth::user();
        $application = SellerApplication::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();

        return view('apply.seller.status', compact('application'));
    }
}
