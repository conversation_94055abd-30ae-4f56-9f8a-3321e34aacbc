@extends('layouts.app')

@section('title', 'Apply to Become a Seller')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Apply to Become a Seller</h2>

                @if (session('success'))
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                        <p>{{ session('success') }}</p>
                    </div>
                @endif

                @if (session('error'))
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                        <p>{{ session('error') }}</p>
                    </div>
                @endif

                <p class="text-gray-600 mb-8">
                    Complete the form below to apply as a seller on Hermosart. Once your application is submitted, our team will review it and get back to you within 2-3 business days.
                </p>

                <form action="{{ route('apply.seller.submit') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Business Information -->
                        <div class="col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Information</h3>
                        </div>

                        <div class="col-span-2">
                            <label for="business_name" class="block text-sm font-medium text-gray-700">Business Name <span class="text-red-500">*</span></label>
                            <input type="text" name="business_name" id="business_name" value="{{ old('business_name') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                            @error('business_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <label for="business_description" class="block text-sm font-medium text-gray-700">Business Description <span class="text-red-500">*</span></label>
                            <textarea name="business_description" id="business_description" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>{{ old('business_description') }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Describe your business, what products you plan to sell, and your experience.</p>
                            @error('business_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Contact Information -->
                        <div class="col-span-2 mt-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                        </div>

                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number <span class="text-red-500">*</span></label>
                            <input type="text" name="phone_number" id="phone_number" value="{{ old('phone_number') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                            @error('phone_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="paypal_email" class="block text-sm font-medium text-gray-700">PayPal Email <span class="text-red-500">*</span></label>
                            <input type="email" name="paypal_email" id="paypal_email" value="{{ old('paypal_email') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#710d17] focus:ring-[#710d17]" required>
                            <p class="mt-1 text-sm text-gray-500">We'll use this email for payments</p>
                            @error('paypal_email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Store Image -->
                        <div class="col-span-2 mt-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Store Image</h3>
                        </div>

                        <div class="col-span-2">
                            <label for="store_image" class="block text-sm font-medium text-gray-700">Store Banner Image</label>
                            <input type="file" name="store_image" id="store_image" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-[#710d17] file:text-white hover:file:bg-[#9a2c39]">
                            <p class="mt-1 text-sm text-gray-500">Upload an image for your store (recommended size: 1200x300px)</p>
                            @error('store_image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="col-span-2 mt-4">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="terms" name="terms" type="checkbox" class="h-4 w-4 text-[#710d17] focus:ring-[#710d17] border-gray-300 rounded" required>
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="terms" class="font-medium text-gray-700">I agree to the <a href="#" class="text-[#710d17] hover:underline">Terms and Conditions</a> and <a href="#" class="text-[#710d17] hover:underline">Seller Policy</a> <span class="text-red-500">*</span></label>
                                    <p class="text-gray-500">By submitting this application, you agree to our terms for sellers and consent to our verification process.</p>
                                </div>
                            </div>
                            @error('terms')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
