<?php

namespace Database\Seeders;

use App\Models\Service;
use App\Models\PortfolioItem;
use App\Models\Professional;
use App\Models\Product;
use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class HomepageSeeder extends Seeder
{
    public function run(): void
    {

        // Portfolio Items (1 per category, fits within max 2)
        PortfolioItem::create([
            'title' => 'E-Commerce Platform',
            'description' => 'Built a scalable site with 99.9% uptime',
            'image' => 'images/portfolio-1.jpg',
            'category' => 'web'
        ]);
        PortfolioItem::create([
            'title' => 'Data Analysis for Retail',
            'description' => 'Delivered predictive analytics for a retail client',
            'image' => 'images/portfolio-2.jpg',
            'category' => 'data'
        ]);
        PortfolioItem::create([
            'title' => 'Customer Service Chatbot',
            'description' => 'AI-powered support system with 85% resolution rate',
            'image' => 'images/portfolio-3.jpg',
            'category' => 'ai'
        ]);
        PortfolioItem::create([
            'title' => 'Brand Identity System',
            'description' => 'Complete rebrand for a tech startup',
            'image' => 'images/portfolio-4.jpg',
            'category' => 'design'
        ]);
        PortfolioItem::create([
            'title' => 'E-Commerce Platform',
            'description' => 'Built a scalable site with 99.9% uptime',
            'image' => 'images/portfolio-1.jpg',
            'category' => 'web'
        ]);
        PortfolioItem::create([
            'title' => 'Data Analysis for Retail',
            'description' => 'Delivered predictive analytics for a retail client',
            'image' => 'images/portfolio-2.jpg',
            'category' => 'data'
        ]);
        PortfolioItem::create([
            'title' => 'Customer Service Chatbot',
            'description' => 'AI-powered support system with 85% resolution rate',
            'image' => 'images/portfolio-3.jpg',
            'category' => 'ai'
        ]);
        PortfolioItem::create([
            'title' => 'Brand Identity System',
            'description' => 'Complete rebrand for a tech startup',
            'image' => 'images/portfolio-4.jpg',
            'category' => 'design'
        ]);
        PortfolioItem::create([
            'title' => 'E-Commerce Platform',
            'description' => 'Built a scalable site with 99.9% uptime',
            'image' => 'images/portfolio-1.jpg',
            'category' => 'web'
        ]);
        PortfolioItem::create([
            'title' => 'Data Analysis for Retail',
            'description' => 'Delivered predictive analytics for a retail client',
            'image' => 'images/portfolio-2.jpg',
            'category' => 'data'
        ]);
        PortfolioItem::create([
            'title' => 'Customer Service Chatbot',
            'description' => 'AI-powered support system with 85% resolution rate',
            'image' => 'images/portfolio-3.jpg',
            'category' => 'ai'
        ]);
        PortfolioItem::create([
            'title' => 'Brand Identity System',
            'description' => 'Complete rebrand for a tech startup',
            'image' => 'images/portfolio-4.jpg',
            'category' => 'design'
        ]);

    }
}
