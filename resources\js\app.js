import './bootstrap';

document.addEventListener("DOMContentLoaded", () => {
  // Mobile menu toggle
  const menuButton = document.querySelector('[aria-controls="mobile-menu"]')
  const mobileMenu = document.getElementById("mobile-menu")

  if (menuButton && mobileMenu) {
    menuButton.addEventListener("click", () => {
      const expanded = menuButton.getAttribute("aria-expanded") === "true"
      menuButton.setAttribute("aria-expanded", !expanded)
      mobileMenu.classList.toggle("hidden")
    })
  }

  // Account type toggle in registration form
  const registrationForm = document.querySelector('form[action*="register"]')
  if (registrationForm) {
    const clientRadio = document.getElementById("client")
    const professionalRadio = document.getElementById("professional")
    const specialtyField = document.getElementById("specialty-field")

    function toggleSpecialtyField() {
      if (professionalRadio && professionalRadio.checked) {
        specialtyField.classList.remove("hidden")
      } else {
        specialtyField.classList.add("hidden")
      }
    }

    if (clientRadio && professionalRadio) {
      clientRadio.addEventListener("change", toggleSpecialtyField)
      professionalRadio.addEventListener("change", toggleSpecialtyField)

      // Initialize on page load
      toggleSpecialtyField()
    }
  }

  // Testimonials carousel
  const testimonials = document.querySelectorAll("[data-testimonial]")
  if (testimonials.length > 0) {
    let currentIndex = 0
    const indicators = document.querySelectorAll("[data-testimonial-indicator]")

    function showTestimonial(index) {
      testimonials.forEach((testimonial, i) => {
        testimonial.classList.toggle("hidden", i !== index)
      })

      if (indicators.length > 0) {
        indicators.forEach((indicator, i) => {
          indicator.classList.toggle("bg-[#710d17]", i === index)
          indicator.classList.toggle("bg-[#9a2c39]/20", i !== index)
        })
      }
    }

    // Initialize
    showTestimonial(currentIndex)

    // Auto rotate testimonials
    setInterval(() => {
      currentIndex = (currentIndex + 1) % testimonials.length
      showTestimonial(currentIndex)
    }, 5000)

    // Click handlers for indicators
    indicators.forEach((indicator, i) => {
      indicator.addEventListener("click", () => {
        currentIndex = i
        showTestimonial(currentIndex)
      })
    })
  }

  // Form validation
  const forms = document.querySelectorAll("form")
  forms.forEach((form) => {
    form.addEventListener("submit", (event) => {
      const requiredFields = form.querySelectorAll("[required]")
      let isValid = true

      requiredFields.forEach((field) => {
        if (!field.value.trim()) {
          isValid = false
          field.classList.add("border-red-500")
        } else {
          field.classList.remove("border-red-500")
        }
      })

      if (!isValid) {
        event.preventDefault()
      }
    })
  })
})
