<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Favorite;
use App\Models\PortfolioItem;
use App\Models\Professional;
use App\Models\Testimonial;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TalentController extends Controller
{
    /**
     * Show the talent page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = Category::all()->toArray();

        // Get up to 3 professionals per category
        $professionals = Professional::with('user')->get()
            ->groupBy('category')
            ->map(function ($group) {
            return $group->take(3)->values()->toArray();
            })
            ->toArray();

        $testimonials = Testimonial::all()->toArray();

        return view('talent', compact('categories', 'professionals', 'testimonials'));
    }

    /**
     * Show a specific professional's profile.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // Find the professional by ID or slug
        $professional = Professional::with('user')
            ->where('id', $slug)
            ->orWhere('slug', $slug)
            ->first();

        if (!$professional) {
            abort(404, 'Professional not found');
        }

        // Get portfolio items
        $portfolioItems = PortfolioItem::where('professional_id', $professional->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get services offered by the professional
        $services = $professional->services()->get();

        // Get products created by the professional
        $products = $professional->products()->get();

        // Check if the professional is favorited by the current user
        $isFavorited = false;
        if (Auth::check()) {
            $isFavorited = Favorite::where('user_id', Auth::id())
                ->where('professional_id', $professional->id)
                ->exists();
        }

        return view('talent-profile', compact('professional', 'portfolioItems', 'services', 'products', 'isFavorited'));
    }
}