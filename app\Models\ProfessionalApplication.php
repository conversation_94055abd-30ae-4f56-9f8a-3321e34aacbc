<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProfessionalApplication extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'professional_title',
        'category',
        'bio',
        'skills',
        'experience_years',
        'education',
        'certifications',
        'portfolio_url',
        'linkedin_url',
        'github_url',
        'behance_url',
        'dribbble_url',
        'other_url',
        'hourly_rate',
        'availability',
        'resume',
        'phone_number',
        'status',
        'rejection_reason',
        'approved_at'
    ];

    protected $casts = [
        'id' => 'string',
        'user_id' => 'string',
        'skills' => 'array',
        'hourly_rate' => 'float',
        'approved_at' => 'datetime',
    ];

    /**
     * Boot function from Laravel.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }

    /**
     * Get the user that owns the application.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
