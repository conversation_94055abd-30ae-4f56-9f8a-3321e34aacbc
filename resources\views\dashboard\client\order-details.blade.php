@extends('layouts.dashboard')

@section('title', 'Order Details')

@section('header', 'Order Details')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.client.projects') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.projects*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="{{ route('dashboard.client.messages') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.messages*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="{{ route('dashboard.client.favorites') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.favorites') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Favorites
        </a>
        <a href="{{ route('dashboard.client.orders') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.orders*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.orders*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Orders
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client.profile') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.profile') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
        </a>
        <a href="{{ route('dashboard.client.settings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.settings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <a href="{{ route('home') }}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Back to Website
    </a>
</div>
@endsection

@section('mobile_sidebar')
<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="{{ route('dashboard.client') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
    <a href="{{ route('dashboard.client.projects') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
    <a href="{{ route('dashboard.client.messages') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
    <a href="{{ route('dashboard.client.favorites') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Favorites</a>
    <a href="{{ route('dashboard.client.orders') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.orders*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Orders</a>
    <a href="{{ route('dashboard.client.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
    <a href="{{ route('dashboard.client.settings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
    <a href="{{ route('home') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
@endsection

@section('content')
<div class="mb-4">
    <a href="{{ route('dashboard.client.orders') }}" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Orders
    </a>
</div>

<div class="bg-white shadow rounded-lg overflow-hidden mb-6">
    <div class="px-4 py-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div>
            <h2 class="text-xl font-bold text-gray-900">Order #{{ substr($order->id, 0, 8) }}</h2>
            <p class="text-sm text-gray-500">Placed on {{ $order->created_at->format('M d, Y') }}</p>
        </div>
        <span class="self-start sm:self-center px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
            @if($order->status == 'Completed') bg-green-100 text-green-800
            @elseif($order->status == 'Processing') bg-blue-100 text-blue-800
            @elseif($order->status == 'Pending') bg-yellow-100 text-yellow-800
            @else bg-red-100 text-red-800
            @endif
        ">
            {{ $order->status }}
        </span>
    </div>

    <div class="px-4 py-4 border-b border-gray-100">
        <h3 class="text-base font-medium text-gray-900 mb-3">{{ $order->order_type === 'service' ? 'Service' : 'Product' }} Information</h3>

        <div class="flex items-center">
            @if($order->order_type === 'service')
                <div class="flex-shrink-0 h-16 w-16 bg-[#fcefcc] rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-medium text-gray-900">{{ $order->service ? $order->service->title : 'Unknown Service' }}</h4>
                    <p class="text-sm text-gray-500">{{ $order->service ? $order->service->description : '' }}</p>
                </div>
            @else
                <div class="flex-shrink-0 h-16 w-16 bg-gray-200 rounded-md flex items-center justify-center overflow-hidden">
                    @if($order->product && $order->product->image)
                    <img src="{{ asset($order->product->image) }}" alt="{{ $order->product->name }}" class="h-16 w-16 rounded-md object-cover">
                    @else
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    @endif
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-medium text-gray-900">{{ $order->product ? $order->product->name : 'Unknown Product' }}</h4>
                    <p class="text-sm text-gray-500">{{ $order->product ? $order->product->description : '' }}</p>
                </div>
            @endif
        </div>
    </div>

    <div class="px-4 py-4 border-b border-gray-100">
        <h3 class="text-base font-medium text-gray-900 mb-3">Order Details</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-500">Price</p>
                <p class="text-sm text-gray-900">${{ number_format($order->price, 2) }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">Payment Method</p>
                <p class="text-sm text-gray-900">{{ $order->payment_method ?? 'Credit Card' }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">Order Date</p>
                <p class="text-sm text-gray-900">{{ $order->created_at->format('M d, Y') }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">Delivery Date</p>
                <p class="text-sm text-gray-900">{{ $order->delivery_date ? $order->delivery_date->format('M d, Y') : 'Pending' }}</p>
            </div>
        </div>
    </div>

    <div class="px-4 py-4">
        <h3 class="text-base font-medium text-gray-900 mb-4">Order Actions</h3>

        @if($order->order_type === 'service')
            @if($order->status === 'Processing')
                <!-- Service Order Actions -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Service Order Status</h4>
                    <p class="text-sm text-blue-800 mb-3">Your payment is being held securely until you mark this order as completed. The professional will contact you to discuss project details.</p>
                    @if($order->auto_complete_date)
                        <p class="text-xs text-blue-700">This order will be automatically completed on {{ $order->auto_complete_date->format('M d, Y') }} if no action is taken.</p>
                    @endif
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <!-- Complete Order Button -->
                    <form action="{{ route('orders.complete', $order->id) }}" method="POST" class="flex-1">
                        @csrf
                        <button type="submit" onclick="return confirm('Are you sure you want to mark this order as completed? This will release the payment to the professional.')" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Mark as Completed
                        </button>
                    </form>

                    <!-- Cancel Order Button -->
                    <button type="button" onclick="openCancelModal()" class="flex-1 inline-flex items-center justify-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Cancel Order
                    </button>
                </div>
            @elseif($order->status === 'Completed')
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <h4 class="text-sm font-medium text-green-900">Order Completed</h4>
                    </div>
                    <p class="text-sm text-green-800 mt-1">This service order has been completed and payment has been released to the professional.</p>
                </div>
            @elseif($order->status === 'Cancelled')
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <h4 class="text-sm font-medium text-red-900">Order Cancelled</h4>
                    </div>
                    <p class="text-sm text-red-800 mt-1">This order has been cancelled.</p>
                    @if($order->cancellation_reason)
                        <p class="text-sm text-red-700 mt-2"><strong>Reason:</strong> {{ $order->cancellation_reason }}</p>
                    @endif
                </div>
            @endif
        @else
            <!-- Product Order Actions -->
            @if($order->status == 'Completed')
            <a href="{{ route('dashboard.client.orders.download', $order->id) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download Files
            </a>
            @endif
        @endif
    </div>
</div>

<!-- Cancel Order Modal -->
<div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Cancel Order</h3>
                <button type="button" onclick="closeCancelModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="{{ route('orders.cancel', $order->id) }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                        Please provide a reason for cancellation:
                    </label>
                    <textarea id="reason" name="reason" rows="4" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[#710d17] focus:border-[#710d17]"
                        placeholder="Explain why you want to cancel this order..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeCancelModal()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Keep Order
                    </button>
                    <button type="submit"
                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Cancel Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openCancelModal() {
    document.getElementById('cancelModal').classList.remove('hidden');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});
</script>

@if(session('info'))
<div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-blue-700">
                {{ session('info') }}
            </p>
        </div>
    </div>
</div>
@endif
@endsection
