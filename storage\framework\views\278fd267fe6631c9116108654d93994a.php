<?php $__env->startSection('title', 'Talent - Hermosart'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h1 class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc]">
                        Our Talent Network
                    </h1>
                    <p class="max-w-[600px] text-zinc-200 md:text-xl">
                        Connect with AI-verified professionals across web, data, AI, and design
                    </p>
                </div>
                <div class="w-full max-w-sm space-y-2">
                    <a href="#talent-categories"
                        class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Find Talent
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        How It Works
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Our AI-powered platform makes it easy to find and hire the perfect talent for your project.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-5xl">
                <div class="grid gap-8 md:grid-cols-3">
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M17.5 21h-10a3.5 3.5 0 0 1 0-7h10a3.5 3.5 0 0 1 0 7Z"></path>
                                <path d="M7.5 7A3.5 3.5 0 0 1 11 3.5h10"></path>
                                <path d="M17.5 14a3.5 3.5 0 0 1 0-7h-10"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">1. Describe Your Project</h3>
                        <p class="text-zinc-600">Tell us about your project requirements, timeline, and budget.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M20 17.5h-10c-1.7 0-3.1-1.3-3.5-3"></path>
                                <path d="M3 6.5h10c1.7 0 3.1 1.3 3.5 3"></path>
                                <path d="M20 6.5v11"></path>
                                <path d="M3 17.5v-11"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">2. AI Matching</h3>
                        <p class="text-zinc-600">Our AI algorithm matches you with the most suitable professionals.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                                <path d="m9 12 2 2 4-4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">3. Hire with Confidence</h3>
                        <p class="text-zinc-600">Review profiles, interview candidates, and hire the perfect match.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Talent Categories Section -->
    <section id="talent-categories" class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-8">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Browse Our Talent
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Explore our diverse pool of AI-verified professionals across various specialties.
                    </p>
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="mx-auto max-w-6xl mb-8">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Search input -->
                        <div class="flex-grow">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <input type="text" id="talent-search"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full pl-10 p-2.5"
                                    placeholder="Search by name, skill, or specialty...">
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <!-- Experience filter -->
                            <div class="w-full sm:w-auto">
                                <select id="experience-filter"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full p-2.5">
                                    <option value="">Experience Level</option>
                                    <option value="beginner">0-2 years</option>
                                    <option value="intermediate">3-5 years</option>
                                    <option value="expert">5+ years</option>
                                </select>
                            </div>

                            <!-- Verification filter -->
                            <div class="w-full sm:w-auto">
                                <select id="verification-filter"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full p-2.5">
                                    <option value="">Verification</option>
                                    <option value="verified">AI-Verified Only</option>
                                    <option value="all">All Professionals</option>
                                </select>
                            </div>

                            <!-- Apply filters button -->
                            <button id="apply-filters"
                                class="bg-[#710d17] hover:bg-[#9a2c39] text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs for talent categories -->
            <div class="mx-auto max-w-6xl">
                <div class="mb-6 border-b border-gray-200">
                    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="talentTabs" role="tablist">
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="mr-2" role="presentation">
                                <button
                                    class="inline-block p-4 border-b-2 <?php echo e($index === 0 ? 'border-[#710d17] text-[#710d17]' : 'border-transparent hover:text-gray-600 hover:border-gray-300'); ?> rounded-t-lg"
                                    id="<?php echo e($category['id']); ?>-tab" data-tab="<?php echo e($category['id']); ?>" type="button"
                                    role="tab" aria-selected="<?php echo e($index === 0 ? 'true' : 'false'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="inline-block mr-2 h-4 w-4">
                                        <?php if($category['icon'] === 'code'): ?>
                                            <polyline points="16 18 22 12 16 6"></polyline>
                                            <polyline points="8 6 2 12 8 18"></polyline>
                                        <?php elseif($category['icon'] === 'database'): ?>
                                            <ellipse cx="12" cy="5" rx="9" ry="3">
                                            </ellipse>
                                            <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                            <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                        <?php elseif($category['icon'] === 'cpu'): ?>
                                            <rect x="4" y="4" width="16" height="16" rx="2"
                                                ry="2"></rect>
                                            <rect x="9" y="9" width="6" height="6"></rect>
                                            <line x1="9" y1="1" x2="9" y2="4"></line>
                                            <line x1="15" y1="1" x2="15" y2="4"></line>
                                            <line x1="9" y1="20" x2="9" y2="23"></line>
                                            <line x1="15" y1="20" x2="15" y2="23"></line>
                                            <line x1="20" y1="9" x2="23" y2="9"></line>
                                            <line x1="20" y1="14" x2="23" y2="14"></line>
                                            <line x1="1" y1="9" x2="4" y2="9"></line>
                                            <line x1="1" y1="14" x2="4" y2="14"></line>
                                        <?php elseif($category['icon'] === 'palette'): ?>
                                            <circle cx="13.5" cy="6.5" r=".5"></circle>
                                            <circle cx="17.5" cy="10.5" r=".5"></circle>
                                            <circle cx="8.5" cy="7.5" r=".5"></circle>
                                            <circle cx="6.5" cy="12.5" r=".5"></circle>
                                            <path
                                                d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z">
                                            </path>
                                        <?php endif; ?>
                                    </svg>
                                    <span class="hidden md:inline"><?php echo e($category['name']); ?></span>
                                </button>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <!-- Tab content -->
                <?php $__currentLoopData = $professionals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $categoryProfessionals): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div id="<?php echo e($category); ?>-content"
                        class="tab-content <?php echo e($category === 'web' ? 'block' : 'hidden'); ?>">
                        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            <?php $__currentLoopData = $categoryProfessionals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="overflow-hidden rounded-lg border bg-white shadow">
                                    <div class="relative">
                                        <img src="<?php echo e($pro['user']['profile_image'] && Storage::exists('public/' . $pro['user']['profile_image']) ? asset('storage/' . $pro['user']['profile_image']) : asset('images/avatar/default/' . rand(1, 10) . '.jpg')); ?>"
                                            alt="<?php echo e($pro['name']); ?>" class="h-48 w-full object-cover">
                                        <?php if($pro['is_aiverified']): ?>
                                            <div class="absolute top-2 right-2">
                                                <span
                                                    class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">AI-Verified</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="p-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <h3 class="text-lg font-bold"><?php echo e($pro['name']); ?></h3>
                                            <div class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="#f59e0b" stroke="#f59e0b" stroke-width="1"
                                                    stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                                    <polygon
                                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                                    </polygon>
                                                </svg>
                                                <span class="text-sm font-medium"><?php echo e($pro['rating']); ?></span>
                                            </div>
                                        </div>
                                        <p class="text-zinc-600 mb-2"><?php echo e($pro['title']); ?></p>
                                        <p class="text-sm text-zinc-600 mb-3"><?php echo e($pro['description']); ?></p>
                                        <div class="flex flex-wrap gap-1 mb-3">
                                            <?php $__currentLoopData = $pro['skills']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span
                                                    class="inline-flex items-center rounded-md border border-[#9a2c39]/30 px-2 py-1 text-xs font-medium">
                                                    <?php echo e($skill); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="flex justify-between text-sm text-zinc-500 mb-4">
                                            <span><?php echo e($pro['experience']); ?> experience</span>
                                            <span><?php echo e($pro['projects']); ?> projects</span>
                                        </div>
                                        <a href="<?php echo e(route('talent.show', $pro['slug'] ?? $pro['id'])); ?>"
                                            class="block w-full rounded-md bg-[#710d17] px-3 py-2 text-sm font-medium text-white hover:bg-[#9a2c39] text-center">
                                            View Profile
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-[#fcefcc]/30">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        What Our Clients Say
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Hear from businesses that have found success with our AI-verified talent.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-4xl">
                <div class="grid gap-8 md:grid-cols-3">
                    <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="rounded-lg border-none bg-white p-6 shadow-sm">
                            <div class="flex items-center gap-4 pb-4">
                                <div class="relative h-12 w-12 overflow-hidden rounded-full">
                                    <img src="<?php echo e($testimonial['avatar'] && Storage::exists('public/' . $testimonial['avatar']) ? asset('storage/' . $testimonial['avatar']) : asset('images/avatar/default/' . rand(1, 10) . '.jpg')); ?>"
                                        alt="<?php echo e($testimonial['name']); ?>" class="h-full w-full object-cover">
                                </div>
                                <div>
                                    <p class="text-lg font-semibold"><?php echo e($testimonial['name']); ?></p>
                                    <p class="text-sm text-zinc-500"><?php echo e($testimonial['role']); ?></p>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="h-8 w-8 text-[#9a2c39]/20 mb-2">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <p class="text-lg text-zinc-700"><?php echo e($testimonial['content']); ?></p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Why Choose Hermosart Talent
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Our platform offers unique advantages for businesses seeking top tech talent.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-5xl">
                <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                                <path d="m9 12 2 2 4-4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">AI-Verified Quality</h3>
                        <p class="text-zinc-600">Our rigorous AI verification ensures you work with only the best
                            professionals.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12"></path>
                                <circle cx="17" cy="7" r="5"></circle>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Perfect Matching</h3>
                        <p class="text-zinc-600">Our AI algorithm matches you with professionals who fit your specific
                            needs.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Secure Platform</h3>
                        <p class="text-zinc-600">Our platform ensures secure payments, contracts, and communication.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M2 3h20"></path>
                                <path d="M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3"></path>
                                <path d="m7 21 5-5 5 5"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Ongoing Support</h3>
                        <p class="text-zinc-600">We provide continuous support throughout your project journey.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Join Talent Pool Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl">Join Our Talent Network</h2>
                    <p class="max-w-[700px] md:text-lg">
                        Are you a tech professional looking for exciting projects? Join our AI-verified talent network
                        today.
                    </p>
                </div>
                <div class="flex flex-col gap-2 min-[400px]:flex-row">
                    <a href="<?php echo e(route('register')); ?>"
                        class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Apply as Talent
                    </a>
                    <a href="#"
                        class="inline-flex items-center justify-center rounded-md border border-[#fcefcc] px-4 py-2 text-sm font-medium text-[#fcefcc] transition-colors hover:bg-[#710d17]/20">
                        Learn More
                    </a>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabs = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('border-[#710d17]', 'text-[#710d17]');
                        t.classList.add('border-transparent', 'hover:text-gray-600',
                            'hover:border-gray-300');
                        t.setAttribute('aria-selected', 'false');
                    });

                    // Add active class to clicked tab
                    tab.classList.add('border-[#710d17]', 'text-[#710d17]');
                    tab.classList.remove('border-transparent', 'hover:text-gray-600',
                        'hover:border-gray-300');
                    tab.setAttribute('aria-selected', 'true');

                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Show the selected tab content
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-content`).classList.remove('hidden');

                    // Reset search and filters when changing tabs
                    document.getElementById('talent-search').value = '';
                    document.getElementById('experience-filter').value = '';
                    document.getElementById('verification-filter').value = '';

                    // Show all professionals in the current tab
                    showAllProfessionals();
                });
            });

            // Search and filter functionality
            const searchInput = document.getElementById('talent-search');
            const experienceFilter = document.getElementById('experience-filter');
            const verificationFilter = document.getElementById('verification-filter');
            const applyFiltersBtn = document.getElementById('apply-filters');

            // Apply filters when button is clicked
            applyFiltersBtn.addEventListener('click', filterProfessionals);

            // Apply filters when Enter key is pressed in search input
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    filterProfessionals();
                }
            });

            function filterProfessionals() {
                const searchTerm = searchInput.value.toLowerCase();
                const experienceValue = experienceFilter.value;
                const verificationValue = verificationFilter.value;

                // Get all professional cards in the currently visible tab content
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const professionalCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow');

                professionalCards.forEach(card => {
                    let shouldShow = true;

                    // Filter by search term
                    if (searchTerm) {
                        const name = card.querySelector('h3').textContent.toLowerCase();
                        const title = card.querySelector('p.text-zinc-600').textContent.toLowerCase();
                        const skills = Array.from(card.querySelectorAll('.flex.flex-wrap.gap-1.mb-3 span'))
                            .map(span => span.textContent.toLowerCase());

                        const matchesSearch = name.includes(searchTerm) ||
                            title.includes(searchTerm) ||
                            skills.some(skill => skill.includes(searchTerm));

                        if (!matchesSearch) {
                            shouldShow = false;
                        }
                    }

                    // Filter by experience
                    if (experienceValue && shouldShow) {
                        const experienceText = card.querySelector(
                                '.flex.justify-between.text-sm.text-zinc-500.mb-4 span:first-child')
                            .textContent;

                        if (experienceValue === 'beginner' && !experienceText.includes('0-2')) {
                            shouldShow = false;
                        } else if (experienceValue === 'intermediate' && !experienceText.includes('3-5')) {
                            shouldShow = false;
                        } else if (experienceValue === 'expert' && !experienceText.includes('5+')) {
                            shouldShow = false;
                        }
                    }

                    // Filter by verification
                    if (verificationValue === 'verified' && shouldShow) {
                        const isVerified = card.querySelector('.absolute.top-2.right-2 span') !== null;
                        if (!isVerified) {
                            shouldShow = false;
                        }
                    }

                    // Show or hide the card
                    card.style.display = shouldShow ? 'block' : 'none';
                });

                // Show a message if no results found
                const visibleCards = Array.from(professionalCards).filter(card => card.style.display !== 'none');
                const noResultsMessage = activeTabContent.querySelector('.no-results-message');

                if (visibleCards.length === 0) {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.className = 'no-results-message text-center py-8';
                        message.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-lg font-medium text-gray-900">No professionals found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                    `;
                        activeTabContent.querySelector('.grid').appendChild(message);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }

            function showAllProfessionals() {
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const professionalCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow');

                professionalCards.forEach(card => {
                    card.style.display = 'block';
                });

                const noResultsMessage = activeTabContent.querySelector('.no-results-message');
                if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\hermosart\resources\views/talent.blade.php ENDPATH**/ ?>