<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check() && (Auth::user()->account_type === 'admin' || Auth::user()->account_type === 'superadmin')) {
            return $next($request);
        }

        return redirect()->route('home')->with('error', 'You do not have access to this area. Admin privileges required.');
    }
}
