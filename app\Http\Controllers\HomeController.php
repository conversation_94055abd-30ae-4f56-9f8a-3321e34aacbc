<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\PortfolioItem;
use App\Models\Professional;
use App\Models\Product;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the application homepage.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all services
        $services = Service::all()->toArray();

        // Get portfolio items - max 2 per category
        $portfolioItems = $this->limitItemsByCategory(
            PortfolioItem::all()->toArray(),
            'category',
            2
        );

        // Get professionals - max 3 per category
        $professionals = $this->limitItemsByCategory(
            Professional::with('user')->get()->toArray(),
            'category',
            3
        );

        // Get products - max 2 per category
        $products = $this->limitItemsByCategory(
            Product::all()->toArray(),
            'category',
            2
        );

        // Get testimonials - max 3 total
        $testimonials = collect(Testimonial::all()->toArray())
            ->take(3)
            ->toArray();

        return view('home', compact('services', 'portfolioItems', 'professionals', 'products', 'testimonials'));
    }

    /**
     * Limit items by category
     *
     * @param array $items
     * @param string $categoryKey
     * @param int $limit
     * @return array
     */
    private function limitItemsByCategory(array $items, string $categoryKey, int $limit): array
    {
        $result = [];
        $categoryCounts = [];

        foreach ($items as $item) {
            $category = $item[$categoryKey];

            if (!isset($categoryCounts[$category])) {
                $categoryCounts[$category] = 0;
            }

            if ($categoryCounts[$category] < $limit) {
                $result[] = $item;
                $categoryCounts[$category]++;
            }
        }

        return $result;
    }
}