<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Client User
        User::create([
            'name' => '<PERSON> Johnson',
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('Cl!3nt@H3rm0s4rt#2024'),
            'account_type' => 'client',
            'is_seller' => false,
            'company' => 'Innovative Solutions Inc.',
            'remember_token' => Str::random(10),
        ]);

        // Create Professional User
        User::create([
            'name' => '<PERSON>',
            'first_name' => 'Ethan',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('Pr0f3ss!0n4l#D3v2024'),
            'account_type' => 'professional',
            'is_seller' => false,
            'specialty' => 'UI/UX Design',
            'company' => 'Creative Design Studio',
            'remember_token' => Str::random(10),
        ]);

        // Create Professional + Seller User
        User::create([
            'name' => 'Sophia Chen',
            'first_name' => 'Sophia',
            'last_name' => 'Chen',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('Pr0S3ll3r@H3rm0#2024!'),
            'account_type' => 'professional',
            'is_seller' => true,
            'specialty' => 'Web Development',
            'company' => 'Web Solutions Plus',
            'remember_token' => Str::random(10),
        ]);

        // Create Seller User
        User::create([
            'name' => 'Noah Williams',
            'first_name' => 'Noah',
            'last_name' => 'Williams',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('S3ll3r@D!g!t4l#2024'),
            'account_type' => 'seller',
            'is_seller' => true,
            'company' => 'Digital Products Co.',
            'remember_token' => Str::random(10),
        ]);

        // Create Admin User
        User::create([
            'name' => 'Olivia Martinez',
            'first_name' => 'Olivia',
            'last_name' => 'Martinez',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('4dm!n@H3rm0s4rt#2024'),
            'account_type' => 'admin',
            'is_seller' => false,
            'company' => 'Hermosart',
            'remember_token' => Str::random(10),
        ]);

        // Create Superadmin User
        User::create([
            'name' => 'Liam Thompson',
            'first_name' => 'Liam',
            'last_name' => 'Thompson',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('Sup3r@dm!n#H3rm0$2024'),
            'account_type' => 'superadmin',
            'is_seller' => false,
            'company' => 'Hermosart',
            'remember_token' => Str::random(10),
        ]);
    }
}
