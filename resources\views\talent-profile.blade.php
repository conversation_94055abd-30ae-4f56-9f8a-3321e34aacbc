@extends('layouts.app')

@section('title', $professional->name . ' - Professional Profile')

@section('content')
<div class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Back button -->
        <div class="mb-6">
            <a href="{{ route('talent') }}" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Professionals
            </a>
        </div>

        <!-- Professional profile header -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
            <div class="px-4 py-5 sm:px-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-20 w-20 bg-gray-200 rounded-full overflow-hidden">
                        @if($professional->user && $professional->user->profile_image)
                        <img src="{{ asset($professional->user->profile_image) }}" alt="{{ $professional->name }}" class="h-full w-full object-cover">
                        @else
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 p-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        @endif
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $professional->name }}</h1>
                        <p class="text-sm text-gray-600">{{ $professional->title }}</p>

                        <div class="mt-2 flex items-center">
                            <div class="flex items-center">
                                @for($i = 0; $i < 5; $i++)
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 {{ $i < ($professional->rating ?? 4) ? 'text-yellow-400' : 'text-gray-300' }}" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @endfor
                            </div>
                            <span class="ml-2 text-sm text-gray-600">{{ $professional->rating ?? '4.0' }} ({{ $professional->reviews_count ?? '24' }} reviews)</span>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-2">
                    @auth
                        @if(auth()->user()->account_type === 'client')
                            <form action="{{ route('dashboard.client.favorites.toggle', $professional->id) }}" method="POST">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-4 py-2 border {{ $isFavorited ? 'border-gray-300 bg-white text-gray-700' : 'border-[#710d17] bg-white text-[#710d17]' }} rounded-md text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 {{ $isFavorited ? 'text-[#710d17] fill-current' : 'text-[#710d17]' }}" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    {{ $isFavorited ? 'Remove from Favorites' : 'Add to Favorites' }}
                                </button>
                            </form>

                            <a href="{{ route('dashboard.client.messages.create', ['recipient_id' => $professional->id]) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                                Contact Professional
                            </a>
                        @endif
                    @else
                        <a href="{{ route('login') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                            </svg>
                            Login to Contact
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <div id="profile-tabs" class="mx-auto mt-8 mb-10">
            <div class="bg-gray-100 rounded-lg p-1 grid grid-cols-2 md:grid-cols-4 gap-1">
                <button data-tab-category="about" class="tab-transition px-2 py-3 text-sm font-medium bg-white focus:outline-none text-center rounded-lg">
                    About
                </button>
                <button data-tab-category="services" class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                    Services
                </button>
                <button data-tab-category="products" class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                    Products
                </button>
                <button data-tab-category="portfolio" class="tab-transition px-2 py-3 text-sm font-medium focus:outline-none text-center rounded-lg">
                    Portfolio
                </button>
            </div>
        </div>

        <div id="profile-content" class="mx-auto py-8">
            <!-- About Tab Content -->
            <div data-tab-content="about" class="tab-content">
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">About</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $professional->description }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Experience</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $professional->experience }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Completed Projects</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $professional->projects }}</dd>
                        </div>
                        @if($professional->skills)
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Skills</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <div class="flex flex-wrap gap-2">
                                    @foreach($professional->skills as $skill)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#fcefcc] text-[#710d17]">{{ $skill }}</span>
                                    @endforeach
                                </div>
                            </dd>
                        </div>
                        @endif
                    </dl>
                </div>
            </div>

            <!-- Services Tab Content -->
            <div data-tab-content="services" class="tab-content">
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Services</h2>
                    @if(isset($services) && count($services) > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($services as $service)
                            @if($service->professional_id == $professional->id)
                            <div class="bg-white shadow rounded-lg p-4 card-hover">
                                <h3 class="text-lg font-semibold text-[#710d17]">{{ $service->name }}</h3>
                                <p class="mt-1 text-sm text-gray-600">{{ $service->description }}</p>
                                <p class="mt-2 text-base font-bold text-gray-900">Rp{{ number_format($service->price, 0, ',', '.') }}</p>
                            </div>
                            @endif
                        @endforeach
                    </div>
                    @else
                    <p class="text-gray-500">No services listed.</p>
                    @endif
                </div>
            </div>

            <!-- Products Tab Content -->
            <div data-tab-content="products" class="tab-content">
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Products</h2>
                    @if(isset($products) && count($products) > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($products as $product)
                            @if($product->professional_id == $professional->id)
                            <div class="bg-white shadow rounded-lg p-4 card-hover">
                                <h3 class="text-lg font-semibold text-[#710d17]">{{ $product->name }}</h3>
                                <p class="mt-1 text-sm text-gray-600">{{ $product->description }}</p>
                                <p class="mt-2 text-base font-bold text-gray-900">Rp{{ number_format($product->price, 0, ',', '.') }}</p>
                            </div>
                            @endif
                        @endforeach
                    </div>
                    @else
                    <p class="text-gray-500">No products listed.</p>
                    @endif
                </div>
            </div>

            <!-- Portfolio Tab Content -->
            <div data-tab-content="portfolio" class="tab-content">
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Portfolio</h2>
                    @if(isset($portfolioItems) && count($portfolioItems) > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($portfolioItems as $item)
                            @if($item->professional_id == $professional->id)
                            <div class="bg-white shadow overflow-hidden rounded-lg card-hover">
                                <div class="h-48 w-full bg-gray-200 flex items-center justify-center overflow-hidden">
                                    @if($item->image)
                                    <img src="{{ asset($item->image) }}" alt="{{ $item->title }}" class="w-full h-full object-cover transition-transform group-hover:scale-105">
                                    @else
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    @endif
                                </div>
                                <div class="p-4">
                                    <h3 class="text-lg font-medium text-[#710d17]">{{ $item->title }}</h3>
                                    <p class="mt-1 text-sm text-gray-500">{{ $item->client_name }}</p>
                                    <p class="mt-2 text-sm text-gray-600 line-clamp-3">{{ $item->description }}</p>
                                </div>
                            </div>
                            @endif
                        @endforeach
                    </div>
                    @else
                    <p class="text-gray-500">No portfolio items listed.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality for Profile section
        setupTabs('profile-tabs', 'profile-content');

        // Function to setup tab functionality
        function setupTabs(tabsContainerId, contentContainerId) {
            const tabsContainer = document.getElementById(tabsContainerId);
            const contentContainer = document.getElementById(contentContainerId);

            if (!tabsContainer || !contentContainer) return;

            const tabs = tabsContainer.querySelectorAll('button');
            const contents = contentContainer.querySelectorAll('[data-tab-content]');

            tabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('active', 'bg-white');
                    });

                    // Add active class to clicked tab
                    tab.classList.add('active', 'bg-white');

                    // Hide all content
                    contents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Show corresponding content
                    const tabCategory = tab.getAttribute('data-tab-category');
                    const targetContent = contentContainer.querySelector(`[data-tab-content="${tabCategory}"]`);

                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }
                });
            });

            // Activate first tab by default
            if (tabs.length > 0) {
                tabs[0].click();
            }
        }
    });
</script>
@endpush

@push('styles')
<style>
    .tab-transition {
        transition: all 0.3s ease-in-out;
    }

    .tab-content {
        transition: opacity 0.3s ease-in-out;
    }

    /* Tab styling */
    .bg-gray-100 {
        background-color: #f3f4f6;
    }

    button.tab-transition {
        font-weight: 500;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    @media (max-width: 768px) {
        button.tab-transition {
            font-size: 0.75rem;
        }
    }

    /* Ensure tabs stack properly on small screens */
    @media (max-width: 767px) {
        .grid-cols-2 button.tab-transition {
            margin-bottom: 0.5rem;
        }
    }

    button.tab-transition.bg-white {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    button.tab-transition:hover {
        background-color: rgba(255, 255, 255, 0.5);
    }

    button.tab-transition.active {
        background-color: white;
    }

    /* Hover effects for cards */
    .card-hover {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
</style>
@endpush
@endsection
