<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('client_id');
            $table->uuid('professional_id');
            $table->string('title');
            $table->text('description');
            $table->enum('status', ['Just Started', 'In Progress', 'Pending Approval', 'Completed', 'Cancelled']);
            $table->date('start_date');
            $table->date('due_date');
            $table->integer('progress')->default(0);
            $table->decimal('budget', 10, 2)->nullable();
            $table->timestamps();

            $table->foreign('client_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('professional_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
