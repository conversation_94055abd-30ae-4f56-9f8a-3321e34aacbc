<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\SellerApplication;
use App\Models\User;

class StoreController extends Controller
{
    /**
     * Show the stores index page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all users who are sellers but not professionals
        $sellers = User::where('is_seller', true)
            ->where('account_type', 'seller')
            ->get();

        return view('stores', compact('sellers'));
    }

    /**
     * Show a specific store profile.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // Find the seller application by business name slug
        $sellerApplication = SellerApplication::where('status', 'approved')
            ->where('business_name_slug', $slug)
            ->first();

        if (!$sellerApplication) {
            abort(404, 'Store not found');
        }

        // Get the user associated with this seller application
        $seller = User::where('id', $sellerApplication->user_id)
            ->where('is_seller', true)
            ->first();

        if (!$seller) {
            abort(404, 'Seller not found');
        }

        // Get products created by the seller
        $products = Product::where('user_id', $seller->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('store-profile', compact('seller', 'sellerApplication', 'products'));
    }


}
