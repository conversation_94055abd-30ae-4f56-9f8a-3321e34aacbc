<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Product extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $casts = [
        'price' => 'float',
        'rating' => 'float',
    ];

    protected $fillable = [
        'name', 'slug', 'price', 'image', 'description', 'rating', 'sales', 'category',
        'professional_id', 'user_id'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Generate UUID if not provided
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }

            // Generate slug if not provided
            if (empty($model->slug)) {
                $model->slug = $model->generateUniqueSlug($model->name);
            }
        });

        static::updating(function ($model) {
            // Update slug if name has changed
            if ($model->isDirty('name') && !$model->isDirty('slug')) {
                $model->slug = $model->generateUniqueSlug($model->name);
            }
        });
    }

    /**
     * Generate a unique slug based on the product's name.
     *
     * @param string $name
     * @return string
     */
    protected function generateUniqueSlug($name)
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $count = 1;

        // Make sure the slug is unique
        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $count++;
        }

        return $slug;
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category', 'id');
    }

    /**
     * Get the professional that created the product.
     */
    public function professional()
    {
        return $this->belongsTo(Professional::class);
    }

    /**
     * Get the user (seller) that created the product.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}