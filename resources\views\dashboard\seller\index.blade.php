@extends('layouts.dashboard')

@section('title', 'Seller Dashboard')

@section('header', 'Dashboard')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.seller') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.seller') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.seller') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.seller.products') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.seller.products*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.seller.products*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Products
        </a>
        <a href="{{ route('dashboard.seller.earnings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.seller.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.seller.earnings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Earnings
        </a>
    </nav>
</div>

@include('dashboard.seller.sidebar-account')
@endsection

@section('mobile_sidebar')
@include('dashboard.seller.mobile-sidebar')
@endsection

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Welcome back, {{ $user->name }}!</h2>
    <p class="mt-1 text-sm text-gray-500">Here's an overview of your seller dashboard.</p>
</div>

<div class="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
    <!-- Stats cards -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-[#710d17]/10 rounded-md p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                        <dd>
                            <div class="text-lg font-medium text-gray-900">{{ $stats['total_products'] }}</div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{{ route('dashboard.seller.products') }}" class="font-medium text-[#710d17] hover:text-[#8a1520]">View all products</a>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                        <dd>
                            <div class="text-lg font-medium text-gray-900">{{ $stats['total_orders'] }}</div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="font-medium text-gray-500">From all your products</span>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Earnings</dt>
                        <dd>
                            <div class="text-lg font-medium text-gray-900">${{ number_format($stats['total_earnings'], 2) }}</div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{{ route('dashboard.seller.earnings') }}" class="font-medium text-[#710d17] hover:text-[#8a1520]">View earnings details</a>
            </div>
        </div>
    </div>
</div>

<div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
    <!-- Recent Products -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Products</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Your most recently added products.</p>
            </div>
            <a href="{{ route('dashboard.seller.products.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#710d17] hover:bg-[#8a1520]">
                Add Product
            </a>
        </div>
        <div class="border-t border-gray-200">
            <ul role="list" class="divide-y divide-gray-200">
                @forelse($recentProducts as $product)
                <li>
                    <a href="{{ route('dashboard.seller.products.show', $product->id) }}" class="block hover:bg-gray-50">
                        <div class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    @if($product->image)
                                    <div class="flex-shrink-0 h-12 w-12 rounded overflow-hidden bg-gray-100">
                                        <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="h-12 w-12 object-cover">
                                    </div>
                                    @else
                                    <div class="flex-shrink-0 h-12 w-12 rounded overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    @endif
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-[#710d17]">{{ $product->name }}</div>
                                        <div class="text-sm text-gray-500">${{ number_format($product->price, 2) }}</div>
                                    </div>
                                </div>
                                <div class="ml-2 flex-shrink-0 flex">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        {{ $product->sales }} sales
                                    </span>
                                </div>
                            </div>
                        </div>
                    </a>
                </li>
                @empty
                <li class="px-4 py-5 sm:px-6 text-center text-gray-500">
                    No products yet. <a href="{{ route('dashboard.seller.products.create') }}" class="text-[#710d17] hover:text-[#8a1520] font-medium">Add your first product</a>.
                </li>
                @endforelse
            </ul>
        </div>
        @if(count($recentProducts) > 0)
        <div class="bg-gray-50 px-4 py-4 sm:px-6">
            <div class="text-sm">
                <a href="{{ route('dashboard.seller.products') }}" class="font-medium text-[#710d17] hover:text-[#8a1520]">View all products</a>
            </div>
        </div>
        @endif
    </div>

    <!-- Recent Orders -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Orders</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">The most recent orders for your products.</p>
        </div>
        <div class="border-t border-gray-200">
            <ul role="list" class="divide-y divide-gray-200">
                @forelse($recentOrders as $order)
                <li class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-[#710d17]">Order #{{ substr($order->id, 0, 8) }}</div>
                            <div class="text-sm text-gray-500">{{ $order->product->name }}</div>
                            <div class="text-sm text-gray-500">Purchased by: {{ $order->user->name }}</div>
                        </div>
                        <div class="ml-2 flex-shrink-0 flex flex-col items-end">
                            <div class="text-sm font-medium text-gray-900">${{ number_format($order->price, 2) }}</div>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                @if($order->status == 'Completed') bg-green-100 text-green-800
                                @elseif($order->status == 'Processing') bg-yellow-100 text-yellow-800
                                @elseif($order->status == 'Cancelled') bg-red-100 text-red-800
                                @else bg-blue-100 text-blue-800 @endif">
                                {{ $order->status }}
                            </span>
                        </div>
                    </div>
                </li>
                @empty
                <li class="px-4 py-5 sm:px-6 text-center text-gray-500">
                    No orders yet. Orders will appear here when customers purchase your products.
                </li>
                @endforelse
            </ul>
        </div>
    </div>
</div>
@endsection
