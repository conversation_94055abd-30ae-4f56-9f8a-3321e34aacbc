@extends('layouts.app')

@section('title', 'Digital Products - Hermosart')

@section('content')
    <!-- Hero Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h1 class="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-[#fcefcc]">
                        Digital Products
                    </h1>
                    <p class="max-w-[600px] text-zinc-200 md:text-xl">
                        Premium digital assets created by our AI-verified talent
                    </p>
                </div>
                <div class="w-full max-w-sm space-y-2">
                    <a href="#products-grid"
                        class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Browse Products
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Featured Products
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Discover our most popular digital products created by top professionals.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-6xl">
                <div class="grid gap-8 md:grid-cols-3">
                    @foreach ($featuredProducts as $product)
                        <div class="overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                            <div class="relative">
                                <div class="grid aspect-video w-full overflow-hidden justify-center items-center">
                                    @if ($product['image'] && Storage::exists('public/' . $product['image']))
                                        <img src="{{ asset('storage/' . $product['image']) }}" alt="{{ $product['name'] }}"
                                            class="h-full w-full object-cover transition-transform hover:scale-105">
                                    @else
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                        </svg>
                                    @endif
                                </div>
                                <div class="absolute top-2 right-2">
                                    <span
                                        class="inline-flex items-center rounded-md bg-[#710d17] px-2 py-1 text-xs font-medium text-white">Featured</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="text-lg font-bold">{{ $product['name'] }}</h3>
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="#f59e0b" stroke="#f59e0b" stroke-width="1"
                                            stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                            <polygon
                                                points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                            </polygon>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $product['rating'] }}</span>
                                    </div>
                                </div>
                                <p class="text-2xl font-bold text-[#710d17] mb-2">${{ $product['price'] }}</p>
                                <p class="text-sm text-zinc-600 mb-4">{{ $product['description'] }}</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-zinc-500">{{ $product['sales'] }} sales</span>
                                    <a href="{{ route('products.show', $product['slug'] ?? $product['id']) }}"
                                        class="inline-flex items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                        View Product
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid Section -->
    <section id="products-grid" class="w-full py-12 md:py-24 lg:py-32 bg-zinc-50">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-8">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Browse All Products
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Explore our complete collection of premium digital products.
                    </p>
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="mx-auto max-w-6xl mb-8">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Search input -->
                        <div class="flex-grow">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <input type="text" id="product-search"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full pl-10 p-2.5"
                                    placeholder="Search by product name or description...">
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <!-- Price filter -->
                            <div class="w-full sm:w-auto">
                                <select id="price-filter"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full p-2.5">
                                    <option value="">Price Range</option>
                                    <option value="0-25">$0 - $25</option>
                                    <option value="25-50">$25 - $50</option>
                                    <option value="50-100">$50 - $100</option>
                                    <option value="100+">$100+</option>
                                </select>
                            </div>

                            <!-- Rating filter -->
                            <div class="w-full sm:w-auto">
                                <select id="rating-filter"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#710d17] focus:border-[#710d17] block w-full p-2.5">
                                    <option value="">Rating</option>
                                    <option value="4+">4+ Stars</option>
                                    <option value="3+">3+ Stars</option>
                                    <option value="2+">2+ Stars</option>
                                </select>
                            </div>

                            <!-- Apply filters button -->
                            <button id="apply-filters"
                                class="bg-[#710d17] hover:bg-[#9a2c39] text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs for product categories -->
            <div class="mx-auto max-w-6xl">
                <div class="mb-6 border-b border-gray-200">
                    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="productTabs" role="tablist">
                        @foreach ($productCategories as $index => $category)
                            <li class="mr-2" role="presentation">
                                <button
                                    class="inline-block p-4 border-b-2 {{ $index === 0 ? 'border-[#710d17] text-[#710d17]' : 'border-transparent hover:text-gray-600 hover:border-gray-300' }} rounded-t-lg"
                                    id="{{ $category['id'] }}-tab" data-tab="{{ $category['id'] }}" type="button"
                                    role="tab" aria-selected="{{ $index === 0 ? 'true' : 'false' }}">
                                    {{ $category['name'] }}
                                </button>
                            </li>
                        @endforeach
                    </ul>
                </div>

                <!-- Tab content -->
                @foreach ($products as $category => $categoryProducts)
                    <div id="{{ $category }}-content"
                        class="tab-content {{ $category === 'web' ? 'block' : 'hidden' }}">
                        <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                            @foreach ($categoryProducts as $product)
                                <div
                                    class="overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                                    <div class="grid aspect-video w-full overflow-hidden justify-center items-center">
                                        @if ($product['image'] && Storage::exists('public/' . $product['image']))
                                            <img src="{{ asset($product['image']) }}" alt="{{ $product['name'] }}"
                                                class="h-full w-full object-cover transition-transform hover:scale-105">
                                        @else
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                            </svg>
                                        @endif
                                    </div>
                                    <div class="p-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <h3 class="text-lg font-bold line-clamp-1">{{ $product['name'] }}</h3>
                                            <div class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="#f59e0b" stroke="#f59e0b" stroke-width="1"
                                                    stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                                    <polygon
                                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                                    </polygon>
                                                </svg>
                                                <span class="text-sm font-medium">{{ $product['rating'] }}</span>
                                            </div>
                                        </div>
                                        <p class="text-xl font-bold text-[#710d17] mb-2">${{ $product['price'] }}</p>
                                        <p class="text-sm text-zinc-600 mb-4 line-clamp-2">{{ $product['description'] }}
                                        </p>
                                        <div class="flex justify-between items-center">
                                            <span class="text-xs text-zinc-500">{{ $product['sales'] }} sales</span>
                                            <a href="{{ route('products.show', $product['slug'] ?? $product['id']) }}"
                                                class="inline-flex items-center justify-center rounded-md bg-[#710d17] px-3 py-1.5 text-sm font-medium text-white hover:bg-[#9a2c39]">
                                                View Product
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">
                        Why Choose Our Products
                    </h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Our digital products offer unique advantages for your projects.
                    </p>
                </div>
            </div>

            <div class="mx-auto max-w-5xl">
                <div class="grid gap-8 md:grid-cols-3">
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                                <path d="m9 12 2 2 4-4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Premium Quality</h3>
                        <p class="text-zinc-600">All products are created by AI-verified professionals and undergo rigorous
                            quality checks.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 2v20"></path>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Cost-Effective</h3>
                        <p class="text-zinc-600">Save time and money by using ready-made solutions that can be customized
                            to your needs.</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-[#fcefcc] mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                fill="none" stroke="#710d17" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.85.83 6.72 2.25"></path>
                                <path d="M21 3v9h-9"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Ongoing Support</h3>
                        <p class="text-zinc-600">Get dedicated support and regular updates for all purchased products.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Custom Work Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-[#fcefcc]/30">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl text-[#710d17]">Need Something Custom?</h2>
                    <p class="max-w-[700px] text-zinc-700 md:text-lg">
                        Our AI-verified talent can create custom digital products tailored to your specific needs.
                    </p>
                </div>
                <div class="flex flex-col gap-2 min-[400px]:flex-row">
                    <a href="#"
                        class="inline-flex items-center justify-center rounded-md bg-[#710d17] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[#9a2c39]">
                        Request Custom Work
                    </a>
                    <a href="#"
                        class="inline-flex items-center justify-center rounded-md border border-[#710d17] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#fcefcc]/50">
                        Contact Sales
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Sell Products Section -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-[#710d17] to-[#9a2c39] text-white">
        <div class="container mx-auto px-4 md:px-6">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl">Are You a Creator?</h2>
                    <p class="max-w-[700px] md:text-lg">
                        Join our marketplace and sell your digital products to a global audience.
                    </p>
                </div>
                <div class="flex flex-col gap-2 min-[400px]:flex-row">
                    <a href="{{ route('register') }}"
                        class="inline-flex items-center justify-center rounded-md bg-[#fcefcc] px-4 py-2 text-sm font-medium text-[#710d17] transition-colors hover:bg-[#f5e5b3]">
                        Become a Seller
                    </a>
                    <a href="#"
                        class="inline-flex items-center justify-center rounded-md border border-[#fcefcc] px-4 py-2 text-sm font-medium text-[#fcefcc] transition-colors hover:bg-[#710d17]/20">
                        Learn More
                    </a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabs = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('border-[#710d17]', 'text-[#710d17]');
                        t.classList.add('border-transparent', 'hover:text-gray-600',
                            'hover:border-gray-300');
                        t.setAttribute('aria-selected', 'false');
                    });

                    // Add active class to clicked tab
                    tab.classList.add('border-[#710d17]', 'text-[#710d17]');
                    tab.classList.remove('border-transparent', 'hover:text-gray-600',
                        'hover:border-gray-300');
                    tab.setAttribute('aria-selected', 'true');

                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Show the selected tab content
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-content`).classList.remove('hidden');

                    // Reset search and filters when changing tabs
                    document.getElementById('product-search').value = '';
                    document.getElementById('price-filter').value = '';
                    document.getElementById('rating-filter').value = '';

                    // Show all products in the current tab
                    showAllProducts();
                });
            });

            // Search and filter functionality
            const searchInput = document.getElementById('product-search');
            const priceFilter = document.getElementById('price-filter');
            const ratingFilter = document.getElementById('rating-filter');
            const applyFiltersBtn = document.getElementById('apply-filters');

            // Apply filters when button is clicked
            applyFiltersBtn.addEventListener('click', filterProducts);

            // Apply filters when Enter key is pressed in search input
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    filterProducts();
                }
            });

            function filterProducts() {
                const searchTerm = searchInput.value.toLowerCase();
                const priceValue = priceFilter.value;
                const ratingValue = ratingFilter.value;

                // Get all product cards in the currently visible tab content
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const productCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow-sm');

                productCards.forEach(card => {
                    let shouldShow = true;

                    // Filter by search term
                    if (searchTerm) {
                        const name = card.querySelector('h3').textContent.toLowerCase();
                        const description = card.querySelector('p.text-sm.text-zinc-600').textContent
                            .toLowerCase();

                        const matchesSearch = name.includes(searchTerm) || description.includes(searchTerm);

                        if (!matchesSearch) {
                            shouldShow = false;
                        }
                    }

                    // Filter by price
                    if (priceValue && shouldShow) {
                        const priceText = card.querySelector('p.text-xl.font-bold.text-\\[\\#710d17\\]')
                            .textContent;
                        const price = parseFloat(priceText.replace('$', '').replace(',', ''));

                        if (priceValue === '0-25' && (price < 0 || price > 25)) {
                            shouldShow = false;
                        } else if (priceValue === '25-50' && (price < 25 || price > 50)) {
                            shouldShow = false;
                        } else if (priceValue === '50-100' && (price < 50 || price > 100)) {
                            shouldShow = false;
                        } else if (priceValue === '100+' && price < 100) {
                            shouldShow = false;
                        }
                    }

                    // Filter by rating
                    if (ratingValue && shouldShow) {
                        const ratingText = card.querySelector('.flex.items-center span').textContent;
                        const rating = parseFloat(ratingText);

                        if (ratingValue === '4+' && rating < 4) {
                            shouldShow = false;
                        } else if (ratingValue === '3+' && rating < 3) {
                            shouldShow = false;
                        } else if (ratingValue === '2+' && rating < 2) {
                            shouldShow = false;
                        }
                    }

                    // Show or hide the card
                    card.style.display = shouldShow ? 'block' : 'none';
                });

                // Show a message if no results found
                const visibleCards = Array.from(productCards).filter(card => card.style.display !== 'none');
                const noResultsMessage = activeTabContent.querySelector('.no-results-message');

                if (visibleCards.length === 0) {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.className = 'no-results-message text-center py-8';
                        message.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-lg font-medium text-gray-900">No products found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                    `;
                        activeTabContent.querySelector('.grid').appendChild(message);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }

            function showAllProducts() {
                const activeTabId = document.querySelector('[data-tab][aria-selected="true"]').getAttribute(
                    'data-tab');
                const activeTabContent = document.getElementById(`${activeTabId}-content`);
                const productCards = activeTabContent.querySelectorAll(
                    '.overflow-hidden.rounded-lg.border.bg-white.shadow-sm');

                productCards.forEach(card => {
                    card.style.display = 'block';
                });

                const noResultsMessage = activeTabContent.querySelector('.no-results-message');
                if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }
        });
    </script>
@endpush
