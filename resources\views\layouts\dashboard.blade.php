<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') - Hermosart Dashboard</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles and Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>


    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Mobile menu toggle -->
        <div x-data="{ open: false }" class="lg:hidden">
            <div class="flex items-center justify-between bg-white px-4 py-3 border-b">
                <a href="{{ route('home') }}" class="flex items-center space-x-2">
                    <div class="relative h-8 w-8 overflow-hidden rounded">
                        <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                            <span class="text-[#fcefcc] font-bold text-lg">H</span>
                        </div>
                    </div>
                    <span class="font-bold">Hermosart</span>
                </a>
                <button @click="open = !open" class="text-gray-500 hover:text-gray-600 focus:outline-none">
                    <svg x-show="!open" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg x-show="open" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="display: none;">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile menu -->
            <div x-show="open" class="bg-white border-b" style="display: none;">
                @yield('mobile_sidebar')
            </div>
        </div>

        <div class="flex flex-1">
            <!-- Sidebar -->
            <aside class="hidden lg:flex lg:flex-col lg:w-64 bg-white border-r">
                @yield('sidebar')
            </aside>

            <!-- Main content -->
            <main class="flex-1">
                <!-- Top navigation -->
                <div class="bg-white border-b">
                    <div class="px-4 py-3 flex items-center justify-between">
                        <h1 class="text-xl font-semibold text-gray-900">@yield('header')</h1>

                        <div class="flex items-center space-x-4">
                            <!-- Notifications -->
                            <div x-data="{ open: false }" class="relative">
                                <button @click="open = !open" class="text-gray-500 hover:text-gray-600 focus:outline-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    <span class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
                                </button>

                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-10" style="display: none;">
                                    <div class="px-4 py-2 border-b">
                                        <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                                    </div>
                                    <div class="max-h-60 overflow-y-auto">
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-50">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0">
                                                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                                                        <span class="text-xs">JD</span>
                                                    </div>
                                                </div>
                                                <div class="ml-3 w-0 flex-1">
                                                    <p class="text-sm font-medium text-gray-900">New message from John Doe</p>
                                                    <p class="text-sm text-gray-500 truncate">Hey, I wanted to discuss the project...</p>
                                                    <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                                                </div>
                                            </div>
                                        </a>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-50">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0">
                                                    <div class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="ml-3 w-0 flex-1">
                                                    <p class="text-sm font-medium text-gray-900">Project approved</p>
                                                    <p class="text-sm text-gray-500 truncate">Your project "Logo Design" has been approved</p>
                                                    <p class="text-xs text-gray-400 mt-1">1 day ago</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="border-t px-4 py-2">
                                        <a href="#" class="text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">View all notifications</a>
                                    </div>
                                </div>
                            </div>

                            <!-- User menu -->
                            <div x-data="{ open: false }" class="relative">
                                <button @click="open = !open" class="flex items-center space-x-2 focus:outline-none">
                                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                                        <span class="text-xs">{{ Auth::user()->name[0] }}</span>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700 hidden md:block">{{ Auth::user()->name }}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10" style="display: none;">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                    <div class="border-t border-gray-100"></div>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            Sign out
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <div class="p-4 md:p-6">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    @stack('scripts')
</body>
</html>
