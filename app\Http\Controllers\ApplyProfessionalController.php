<?php

namespace App\Http\Controllers;

use App\Models\ProfessionalApplication;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ApplyProfessionalController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the professional application form.
     *
     * @return \Illuminate\View\View
     */
    public function showApplicationForm()
    {
        $user = Auth::user();

        // Check if user already has a pending or approved application
        $existingApplication = ProfessionalApplication::where('user_id', $user->id)
            ->whereIn('status', ['pending', 'approved'])
            ->first();

        if ($existingApplication) {
            return redirect()->route('apply.professional.status')
                ->with('info', 'You already have an application in progress.');
        }

        // Check if user is already a professional
        if ($user->account_type === 'professional') {
            return redirect()->route('home')
                ->with('info', 'You are already registered as a professional.');
        }

        // Get categories for the dropdown
        $categories = Category::all();

        return view('apply.professional.form', compact('user', 'categories'));
    }

    /**
     * Process the professional application submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitApplication(Request $request)
    {
        $user = Auth::user();

        // Validate the form data
        $validator = Validator::make($request->all(), [
            'professional_title' => 'required|string|max:255',
            'category' => 'required|string|exists:categories,id',
            'bio' => 'required|string|max:2000',
            'skills' => 'required|array|min:1',
            'skills.*' => 'required|string|max:50',
            'experience_years' => 'required|string|max:50',
            'education' => 'required|string|max:255',
            'certifications' => 'nullable|string|max:1000',
            'portfolio_url' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'github_url' => 'nullable|url|max:255',
            'behance_url' => 'nullable|url|max:255',
            'dribbble_url' => 'nullable|url|max:255',
            'other_url' => 'nullable|url|max:255',
            'hourly_rate' => 'nullable|numeric|min:0',
            'availability' => 'nullable|string|max:255',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            'phone_number' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle file uploads
        $profileImagePath = null;
        $resumePath = null;

        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('user/profile_images', 'public');

            // Update user's profile_image field
            $userModel = User::find($user->id);
            $userModel->profile_image = $profileImagePath;
            $userModel->save();
        }

        if ($request->hasFile('resume')) {
            $resumePath = $request->file('resume')->store('professional/resumes', 'public');
        }

        // Create the application
        $application = new ProfessionalApplication([
            'user_id' => $user->id,
            'professional_title' => $request->professional_title,
            'category' => $request->category,
            'bio' => $request->bio,
            'skills' => $request->skills,
            'experience_years' => $request->experience_years,
            'education' => $request->education,
            'certifications' => $request->certifications,
            'portfolio_url' => $request->portfolio_url,
            'linkedin_url' => $request->linkedin_url,
            'github_url' => $request->github_url,
            'behance_url' => $request->behance_url,
            'dribbble_url' => $request->dribbble_url,
            'other_url' => $request->other_url,
            'hourly_rate' => $request->hourly_rate,
            'availability' => $request->availability,

            'resume' => $resumePath,
            'phone_number' => $request->phone_number,
            'status' => 'pending'
        ]);

        $application->save();

        return redirect()->route('dashboard.professional')
            ->with('success', 'Your professional application has been submitted successfully.');
    }

    /**
     * Show the application status page.
     *
     * @return \Illuminate\View\View
     */
    public function showApplicationStatus()
    {
        $user = Auth::user();
        $application = ProfessionalApplication::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();

        return view('apply.professional.status', compact('application'));
    }
}
