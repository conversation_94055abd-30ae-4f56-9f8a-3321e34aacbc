<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Http\Request;

class SellerApplicationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of the seller applications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $applications = SellerApplication::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.seller-applications.index', compact('applications'));
    }

    /**
     * Display the specified seller application.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $application = SellerApplication::with('user')->findOrFail($id);

        return view('admin.seller-applications.show', compact('application'));
    }

    /**
     * Approve the specified seller application.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve($id)
    {
        $application = SellerApplication::findOrFail($id);

        // Only pending applications can be approved
        if ($application->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending applications can be approved.');
        }

        // Update the application status
        $application->status = 'approved';
        $application->approved_at = now();
        $application->save();

        // Update the user's seller status
        $user = User::findOrFail($application->user_id);
        $user->is_seller = true;

        // If the user is not already a professional or seller, update their account type
        if ($user->account_type === 'client') {
            $user->account_type = 'seller';
        }

        $user->save();

        return redirect()->route('admin.seller-applications.index')
            ->with('success', 'Seller application approved successfully.');
    }

    /**
     * Reject the specified seller application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, $id)
    {
        $application = SellerApplication::findOrFail($id);

        // Only pending applications can be rejected
        if ($application->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending applications can be rejected.');
        }

        // Validate the rejection reason
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        // Update the application status
        $application->status = 'rejected';
        $application->rejection_reason = $request->rejection_reason;
        $application->save();

        return redirect()->route('admin.seller-applications.index')
            ->with('success', 'Seller application rejected successfully.');
    }


}
