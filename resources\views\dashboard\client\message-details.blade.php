@extends('layouts.dashboard')

@section('title', 'Message Details')

@section('header', 'Message Details')

@section('sidebar')
<div class="p-4">
    <a href="{{ route('home') }}" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="{{ route('dashboard.client.projects') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.projects*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="{{ route('dashboard.client.messages') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.messages*') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="{{ route('dashboard.client.favorites') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.favorites') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Favorites
        </a>
        <a href="{{ route('dashboard.client.orders') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.orders') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Orders
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
    <nav class="mt-2 space-y-1">
        <a href="{{ route('dashboard.client.profile') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.profile') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
        </a>
        <a href="{{ route('dashboard.client.settings') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.client.settings') ? 'text-white' : 'text-gray-400' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <a href="{{ route('home') }}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Back to Website
    </a>
</div>
@endsection

@section('mobile_sidebar')
<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="{{ route('dashboard.client') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
    <a href="{{ route('dashboard.client.projects') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.projects*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
    <a href="{{ route('dashboard.client.messages') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.messages*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
    <a href="{{ route('dashboard.client.favorites') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Favorites</a>
    <a href="{{ route('dashboard.client.orders') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Orders</a>
    <a href="{{ route('dashboard.client.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
    <a href="{{ route('dashboard.client.settings') }}" class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
    <a href="{{ route('home') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
@endsection

@section('content')
<div class="mb-4">
    <a href="{{ route('dashboard.client.messages') }}" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Messages
    </a>
</div>

@if(session('success'))
<div class="mb-4 rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
        </div>
        <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
@endif

<div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- Message Header -->
    <div class="px-4 py-4 border-b border-gray-100">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <h1 class="text-lg font-semibold text-gray-900">
                {{ preg_replace('/^(Re: )+/', '', $message->subject) }}
            </h1>

            @if($message->project)
            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                {{ $message->project->title }}
            </span>
            @endif
        </div>

        <div class="mt-2 text-sm text-gray-500">
            Conversation with
            <span class="font-medium">
                {{ $message->sender_id == Auth::id() ? $message->recipient->name : $message->sender->name }}
            </span>
        </div>
    </div>

    <!-- Chat Messages -->
    <div class="px-4 py-4 h-[calc(100vh-24rem)] sm:h-[calc(100vh-20rem)] overflow-y-auto" id="chat-messages">
        <!-- Related Messages -->
        @foreach($relatedMessages as $relatedMessage)
        <div class="flex {{ $relatedMessage->sender_id == Auth::id() ? 'justify-end' : 'justify-start' }} mb-4">
            <div class="flex max-w-[75%] sm:max-w-md {{ $relatedMessage->sender_id == Auth::id() ? 'flex-row-reverse' : '' }}">
                <div class="flex-shrink-0 {{ $relatedMessage->sender_id == Auth::id() ? 'ml-2' : 'mr-2' }}">
                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                        <span class="text-xs">{{ substr($relatedMessage->sender_id == Auth::id() ? Auth::user()->name : $relatedMessage->sender->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="{{ $relatedMessage->sender_id == Auth::id() ? 'bg-[#710d17] text-white' : 'bg-gray-100 text-gray-800' }} rounded-lg p-3 shadow-sm">
                    <div class="text-sm whitespace-pre-line break-words">{{ $relatedMessage->content }}</div>
                    <div class="mt-1 text-xs {{ $relatedMessage->sender_id == Auth::id() ? 'text-gray-200' : 'text-gray-500' }} text-right">
                        {{ $relatedMessage->created_at->format('M d, g:i A') }}
                    </div>
                </div>
            </div>
        </div>
        @endforeach

        <!-- Current Message -->
        <div class="flex {{ $message->sender_id == Auth::id() ? 'justify-end' : 'justify-start' }} mb-4">
            <div class="flex max-w-[75%] sm:max-w-md {{ $message->sender_id == Auth::id() ? 'flex-row-reverse' : '' }}">
                <div class="flex-shrink-0 {{ $message->sender_id == Auth::id() ? 'ml-2' : 'mr-2' }}">
                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                        <span class="text-xs">{{ substr($message->sender_id == Auth::id() ? Auth::user()->name : $message->sender->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="{{ $message->sender_id == Auth::id() ? 'bg-[#710d17] text-white' : 'bg-gray-100 text-gray-800' }} rounded-lg p-3 shadow-sm">
                    <div class="text-sm whitespace-pre-line break-words">{{ $message->content }}</div>
                    <div class="mt-1 text-xs {{ $message->sender_id == Auth::id() ? 'text-gray-200' : 'text-gray-500' }} text-right">
                        {{ $message->created_at->format('M d, g:i A') }}
                    </div>
                </div>
            </div>
        </div>

        @if($relatedMessages->isEmpty() && $message->sender_id != Auth::id())
        <div class="text-center text-sm text-gray-500 my-4 py-2">
            <p>This is the start of your conversation with {{ $message->sender->name }}.</p>
        </div>
        @endif
    </div>

    <!-- Reply Section -->
    <div class="px-4 py-4 bg-gray-50 border-t border-gray-100">
        <form id="messageForm" action="{{ route('dashboard.client.messages.store') }}" method="POST" class="space-y-3">
            @csrf
            <input type="hidden" name="recipient_id" value="{{ $message->sender_id == Auth::id() ? $message->recipient_id : $message->sender_id }}">
            <input type="hidden" name="subject" value="{{ preg_replace('/^(Re: )+/', 'Re: ', $message->subject) }}">
            <input type="hidden" name="message_id" value="{{ $message->id }}">
            @if($message->project_id)
            <input type="hidden" name="project_id" value="{{ $message->project_id }}">
            @endif

            <div class="flex items-end space-x-2">
                <div class="flex-grow">
                    <textarea
                        id="messageContent"
                        name="content"
                        rows="2"
                        class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-[#710d17] focus:border-[#710d17] text-sm"
                        required
                        placeholder="Type your message here..."
                    ></textarea>
                </div>
                <div>
                    <button
                        type="submit"
                        id="sendButton"
                        class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatMessages = document.getElementById('chat-messages');
        const messageForm = document.getElementById('messageForm');
        const messageContent = document.getElementById('messageContent');
        const sendButton = document.getElementById('sendButton');

        // Scroll to bottom of chat messages on page load
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Make Enter key send the message (Shift+Enter for new line)
        messageContent.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (messageContent.value.trim() !== '') {
                    sendButton.disabled = true;
                    sendButton.classList.add('opacity-75');
                    messageForm.submit();
                }
            }
        });

        // Submit form via AJAX to stay on the same page
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (messageContent.value.trim() === '') {
                return;
            }

            // Disable the send button to prevent double-sending
            sendButton.disabled = true;
            sendButton.classList.add('opacity-75');

            // Create FormData object
            const formData = new FormData(messageForm);

            // Send the message via AJAX
            fetch(messageForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add the new message to the chat
                    const newMessageHtml = `
                        <div class="flex justify-end mb-4">
                            <div class="flex max-w-xs sm:max-w-md flex-row-reverse">
                                <div class="flex-shrink-0 ml-2">
                                    <div class="h-8 w-8 rounded-full bg-[#710d17] flex items-center justify-center text-white">
                                        <span class="text-xs">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="bg-[#710d17] text-white rounded-lg p-3 shadow-sm">
                                    <div class="text-sm whitespace-pre-line">${messageContent.value}</div>
                                    <div class="mt-1 text-xs text-gray-200 text-right">
                                        Just now
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    chatMessages.insertAdjacentHTML('beforeend', newMessageHtml);

                    // Clear the textarea
                    messageContent.value = '';

                    // Scroll to the bottom of the chat
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    // Re-enable the send button
                    sendButton.disabled = false;
                    sendButton.classList.remove('opacity-75');
                } else {
                    alert('Failed to send message. Please try again.');
                    sendButton.disabled = false;
                    sendButton.classList.remove('opacity-75');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending the message. Please try again.');
                sendButton.disabled = false;
                sendButton.classList.remove('opacity-75');
            });
        });

        // Poll for new messages every 10 seconds
        function checkForNewMessages() {
            fetch(`/dashboard/client/messages/check-new/${messageForm.querySelector('input[name="message_id"]').value}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.hasNewMessages) {
                    // Reload the messages container
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
        }

        // Check for new messages every 10 seconds
        setInterval(checkForNewMessages, 10000);
    });
</script>
@endsection
