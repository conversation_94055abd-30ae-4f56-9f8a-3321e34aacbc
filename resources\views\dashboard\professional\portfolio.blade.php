@extends('layouts.dashboard')

@section('title', 'Portfolio')

@section('header', 'Portfolio')

@section('sidebar')
    <div class="p-4">
        <a href="{{ route('home') }}" class="flex items-center space-x-2">
            <div class="relative h-8 w-8 overflow-hidden rounded">
                <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                    <span class="text-[#fcefcc] font-bold text-lg">H</span>
                </div>
            </div>
            <span class="font-bold">Hermosart</span>
        </a>
    </div>

    <div class="mt-6 px-4">
        <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
        <nav class="mt-2 space-y-1">
            <a href="{{ route('dashboard.professional') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Dashboard
            </a>
            <a href="{{ route('dashboard.professional.projects') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.projects') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Projects
            </a>
            <a href="{{ route('dashboard.professional.messages') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.messages') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                Messages
            </a>
            <a href="{{ route('dashboard.professional.portfolio') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.portfolio') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.portfolio') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Portfolio
            </a>
            <a href="{{ route('dashboard.professional.products.create') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.products*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.products*') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Products
            </a>
            <a href="{{ route('dashboard.professional.services.create') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.services*') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.services*') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Services
            </a>
            <a href="{{ route('dashboard.professional.earnings') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.earnings') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Earnings
            </a>
        </nav>
    </div>

    <div class="mt-6 px-4">
        <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
        <nav class="mt-2 space-y-1">
            <a href="{{ route('dashboard.professional.profile') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.profile') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Profile
            </a>
            <a href="{{ route('dashboard.professional.settings') }}"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard.professional.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 {{ request()->routeIs('dashboard.professional.settings') ? 'text-white' : 'text-gray-400' }}"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Settings
            </a>
        </nav>
    </div>

    <div class="mt-6 px-4">
        <a href="{{ route('home') }}"
            class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Back to Website
        </a>
    </div>
@endsection

@section('mobile_sidebar')
    <div class="px-2 pt-2 pb-3 space-y-1">
        <a href="{{ route('dashboard.professional') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Dashboard</a>
        <a href="{{ route('dashboard.professional.projects') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Projects</a>
        <a href="{{ route('dashboard.professional.messages') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Messages</a>
        <a href="{{ route('dashboard.professional.portfolio') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.portfolio') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Portfolio</a>
        <a href="{{ route('dashboard.professional.earnings') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.earnings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Earnings</a>
        <a href="{{ route('dashboard.professional.profile') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Profile</a>
        <a href="{{ route('dashboard.professional.settings') }}"
            class="block px-3 py-2 rounded-md text-base font-medium {{ request()->routeIs('dashboard.professional.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100' }}">Settings</a>
        <a href="{{ route('home') }}"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
    </div>
@endsection

@section('content')
    <div class="mb-6 flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">My Portfolio</h2>
        <a href="{{ route('dashboard.professional.portfolio.create') }}"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Portfolio Item
        </a>
    </div>

    @if (session('success'))
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('success') }}
        </div>
    @endif

    <div class="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        @forelse($portfolioItems as $item)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="relative">
                    <div class="h-48 w-full bg-gray-200 flex items-center justify-center">
                        @if ($item->image && Storage::exists('public/' . $item->image))
                            <img src="{{ asset('storage/' . $item->image) }}" alt="{{ $item->title }}"
                                class="h-48 w-full object-cover">
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        @endif
                    </div>
                    @if ($item->featured)
                        <div class="absolute top-2 left-2 px-2 py-1 bg-[#710d17] text-white text-xs font-bold rounded">
                            Featured
                        </div>
                    @endif
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <a href="{{ route('dashboard.professional.portfolio.edit', $item->id) }}"
                            class="p-1 rounded-full bg-white shadow hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                        </a>
                        <form action="{{ route('dashboard.professional.portfolio.destroy', $item->id) }}" method="POST"
                            class="inline"
                            onsubmit="return confirm('Are you sure you want to delete this portfolio item?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="p-1 rounded-full bg-white shadow hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-lg font-bold text-gray-900">{{ $item->title }}</h3>

                    <div class="mt-2 flex items-center">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $item->category }}
                        </span>
                        @if ($item->completion_date)
                            <span class="ml-2 text-xs text-gray-500">{{ $item->completion_date->format('M Y') }}</span>
                        @endif
                    </div>

                    <p class="mt-2 text-sm text-gray-600">{{ Str::limit($item->description, 100) }}</p>

                    @if ($item->client_name)
                        <div class="mt-3 text-sm text-gray-500">
                            <span class="font-medium">Client:</span> {{ $item->client_name }}
                        </div>
                    @endif

                    <div class="mt-4 flex justify-end">
                        <a href="{{ route('dashboard.professional.portfolio.show', $item->id) }}"
                            class="text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">View details</a>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full bg-white overflow-hidden shadow rounded-lg p-6 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">No portfolio items yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by adding your first portfolio item.</p>
                <div class="mt-6">
                    <a href="{{ route('dashboard.professional.portfolio.create') }}"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add Portfolio Item
                    </a>
                </div>
            </div>
        @endforelse
    </div>
@endsection
