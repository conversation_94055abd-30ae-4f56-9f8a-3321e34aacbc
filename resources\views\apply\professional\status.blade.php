@extends('layouts.app')

@section('title', 'Professional Application Status')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header with background image -->
            <div class="relative h-48 bg-gradient-to-r from-[#710d17] to-[#9a2c39]">
                <div class="absolute inset-0 bg-opacity-70 flex items-center justify-center">
                    <h1 class="text-3xl font-bold text-white text-center px-4">Professional Application Status</h1>
                </div>
            </div>

            <div class="px-6 py-8 md:p-10">
                @if (session('success'))
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md" role="alert">
                        <p>{{ session('success') }}</p>
                    </div>
                @endif

                @if (session('error'))
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md" role="alert">
                        <p>{{ session('error') }}</p>
                    </div>
                @endif

                @if (session('info'))
                    <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6 rounded-md" role="alert">
                        <p>{{ session('info') }}</p>
                    </div>
                @endif

                @if (!$application)
                    <div class="text-center py-12">
                        <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No application found</h3>
                        <p class="mt-2 text-base text-gray-500">You haven't submitted a professional application yet.</p>
                        <div class="mt-8">
                            <a href="{{ route('apply.professional.form') }}" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17] transition-colors duration-200">
                                Apply to become a professional
                            </a>
                        </div>
                    </div>
                @else
                    <div class="space-y-8">
                        <!-- Application Status Card -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                            <div class="px-6 py-5 bg-gray-100 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Application Status
                                </h3>
                            </div>
                            <div class="px-6 py-5">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Current Status</p>
                                        <div class="mt-1">
                                            @if ($application->status == 'pending')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                                    <svg class="mr-1.5 h-2 w-2 text-yellow-600" fill="currentColor" viewBox="0 0 8 8">
                                                        <circle cx="4" cy="4" r="3" />
                                                    </svg>
                                                    Pending Review
                                                </span>
                                            @elseif ($application->status == 'approved')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                                    <svg class="mr-1.5 h-2 w-2 text-green-600" fill="currentColor" viewBox="0 0 8 8">
                                                        <circle cx="4" cy="4" r="3" />
                                                    </svg>
                                                    Approved
                                                </span>
                                            @elseif ($application->status == 'rejected')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                                    <svg class="mr-1.5 h-2 w-2 text-red-600" fill="currentColor" viewBox="0 0 8 8">
                                                        <circle cx="4" cy="4" r="3" />
                                                    </svg>
                                                    Rejected
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Submission Date</p>
                                        <p class="mt-1 text-sm text-gray-900">{{ $application->created_at->format('F j, Y') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Application ID</p>
                                        <p class="mt-1 text-sm text-gray-900">{{ substr($application->id, 0, 8) }}</p>
                                    </div>
                                </div>

                                @if ($application->status == 'pending')
                                    <div class="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-yellow-700">
                                                    Your application is currently under review. This process typically takes 2-3 business days. We'll notify you by email once a decision has been made.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @elseif ($application->status == 'approved')
                                    <div class="mt-6 bg-green-50 border-l-4 border-green-400 p-4 rounded-md">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-green-700">
                                                    Congratulations! Your application has been approved on {{ $application->approved_at->format('F j, Y') }}. You can now access your professional dashboard and start offering your services on Hermosart.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-6 flex justify-center">
                                        <a href="{{ route('dashboard.professional') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                            Go to Professional Dashboard
                                        </a>
                                    </div>
                                @elseif ($application->status == 'rejected')
                                    <div class="mt-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-red-700">
                                                    Unfortunately, your application has been rejected. Please review the reason provided below and consider reapplying with the necessary improvements.
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    @if ($application->rejection_reason)
                                        <div class="mt-4 bg-gray-100 p-4 rounded-md">
                                            <h4 class="text-sm font-medium text-gray-900">Reason for Rejection:</h4>
                                            <p class="mt-1 text-sm text-gray-700">{{ $application->rejection_reason }}</p>
                                        </div>
                                    @endif

                                    <div class="mt-6 flex justify-center">
                                        <a href="{{ route('apply.professional.form') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                            Apply Again
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Application Details Card -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                            <div class="px-6 py-5 bg-gray-100 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-[#710d17]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Application Details
                                </h3>
                            </div>
                            <div class="px-6 py-5 divide-y divide-gray-200">
                                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500">Professional Title</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $application->professional_title }}</dd>
                                </div>
                                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $application->category }}</dd>
                                </div>
                                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500">Experience</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $application->experience_years }}</dd>
                                </div>
                                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500">Skills</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($application->skills as $skill)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {{ $skill }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </dd>
                                </div>

                                @if ($application->user->profile_image)
                                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Profile Image</dt>
                                        <dd class="mt-1 sm:mt-0 sm:col-span-2">
                                            <img src="{{ asset('storage/' . $application->user->profile_image) }}" alt="Profile Image" class="h-32 w-32 object-cover rounded-lg">
                                        </dd>
                                    </div>
                                @endif

                                @if ($application->resume)
                                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                                        <dt class="text-sm font-medium text-gray-500">Resume</dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <a href="{{ asset('storage/' . $application->resume) }}" target="_blank" class="text-[#710d17] hover:underline flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                                </svg>
                                                View Resume
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
