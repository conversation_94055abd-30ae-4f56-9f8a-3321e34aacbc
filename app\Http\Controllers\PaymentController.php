<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Product;
use App\Models\Service;
use App\Models\Order;
use App\Models\Earning;
use App\Models\Message;
use App\Services\PayPalService;

class PaymentController extends Controller
{
    private $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    /**
     * Initiate PayPal payment for a service
     */
    public function initiateServicePayment(Request $request, $serviceId)
    {
        try {
            // Validate user is authenticated
            if (!Auth::check()) {
                return redirect()->route('login')->with('error', 'Please login to purchase services.');
            }

            $user = Auth::user();
            $service = Service::findOrFail($serviceId);

            // Check if user is trying to buy their own service
            if ($service->professional_id === $user->id) {
                return redirect()->back()->with('error', 'You cannot purchase your own service.');
            }

            // Create order record with pending status
            $order = Order::create([
                'user_id' => $user->id,
                'service_id' => $service->id,
                'order_type' => 'service',
                'price' => $service->price,
                'status' => 'Pending',
                'payment_method' => 'paypal'
            ]);

            // Prepare PayPal payment
            $returnUrl = route('payment.success', ['orderId' => $order->id]);
            $cancelUrl = route('payment.cancel', ['orderId' => $order->id]);

            $description = "Purchase of {$service->title} service from Hermosart";

            // Create PayPal payment
            $payment = $this->paypalService->createPayment(
                $service->price,
                $description,
                $returnUrl,
                $cancelUrl
            );

            // Store PayPal payment ID in order
            $order->update([
                'paypal_payment_id' => $payment['id'],
                'paypal_payment_details' => $payment
            ]);

            // Find approval URL and redirect user to PayPal
            foreach ($payment['links'] as $link) {
                if ($link['rel'] === 'approval_url') {
                    return redirect($link['href']);
                }
            }

            throw new \Exception('PayPal approval URL not found');

        } catch (\Exception $e) {
            Log::error('Service payment initiation error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to initiate payment. Please try again.');
        }
    }

    /**
     * Initiate PayPal payment for a product
     */
    public function initiatePayment(Request $request, $productId)
    {
        try {
            // Validate user is authenticated
            if (!Auth::check()) {
                return redirect()->route('login')->with('error', 'Please login to purchase products.');
            }

            $user = Auth::user();
            $product = Product::findOrFail($productId);

            // Check if user is trying to buy their own product
            if ($product->user_id === $user->id) {
                return redirect()->back()->with('error', 'You cannot purchase your own product.');
            }

            // Create order record with pending status
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'price' => $product->price,
                'status' => 'Pending',
                'payment_method' => 'paypal'
            ]);

            // Prepare PayPal payment
            $returnUrl = route('payment.success', ['orderId' => $order->id]);
            $cancelUrl = route('payment.cancel', ['orderId' => $order->id]);

            $description = "Purchase of {$product->name} from Hermosart";

            // Create PayPal payment
            $payment = $this->paypalService->createPayment(
                $product->price,
                $description,
                $returnUrl,
                $cancelUrl
            );

            // Store PayPal payment ID in order
            $order->update([
                'paypal_payment_id' => $payment['id'],
                'paypal_payment_details' => $payment
            ]);

            // Find approval URL and redirect user to PayPal
            foreach ($payment['links'] as $link) {
                if ($link['rel'] === 'approval_url') {
                    return redirect($link['href']);
                }
            }

            throw new \Exception('PayPal approval URL not found');

        } catch (\Exception $e) {
            Log::error('Payment initiation error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to initiate payment. Please try again.');
        }
    }

    /**
     * Handle successful PayPal payment
     */
    public function paymentSuccess(Request $request, $orderId)
    {
        try {
            $order = Order::findOrFail($orderId);
            $paymentId = $request->get('paymentId');
            $payerId = $request->get('PayerID');

            if (!$paymentId || !$payerId) {
                throw new \Exception('Missing payment parameters');
            }

            // Execute PayPal payment
            $executedPayment = $this->paypalService->executePayment($paymentId, $payerId);

            if ($executedPayment['state'] === 'approved') {
                DB::transaction(function () use ($order, $payerId, $executedPayment) {
                    // Update order status - Processing for services, Completed for products
                    $status = $order->order_type === 'service' ? 'Processing' : 'Completed';
                    $autoCompleteDate = $order->order_type === 'service' ? now()->addDays(30) : null;

                    $order->update([
                        'status' => $status,
                        'paypal_payer_id' => $payerId,
                        'transaction_id' => $executedPayment['id'],
                        'paypal_payment_details' => $executedPayment,
                        'auto_complete_date' => $autoCompleteDate
                    ]);

                    // Create earning record for the seller/professional
                    if ($order->order_type === 'service') {
                        $service = $order->service;
                        if ($service->professional_id) {
                            Earning::create([
                                'professional_id' => $service->professional_id,
                                'order_id' => $order->id,
                                'amount' => $order->price * 0.85, // 85% to professional, 15% platform fee
                                'description' => "Sale of {$service->title} service",
                                'status' => 'Pending',
                                'transaction_id' => $executedPayment['id']
                            ]);
                        }
                        // Update service sales count
                        $service->increment('sales');

                        // Send automatic message to professional
                        $this->sendServicePurchaseMessage($order);
                    } else {
                        $product = $order->product;
                        if ($product->user_id) {
                            Earning::create([
                                'professional_id' => $product->user_id,
                                'order_id' => $order->id,
                                'amount' => $order->price * 0.85, // 85% to seller, 15% platform fee
                                'description' => "Sale of {$product->name}",
                                'status' => 'Pending',
                                'transaction_id' => $executedPayment['id']
                            ]);
                        }
                        // Update product sales count
                        $product->increment('sales');
                    }
                });

                return redirect()->route('payment.success.page', ['orderId' => $order->id])
                    ->with('success', 'Payment completed successfully!');
            } else {
                throw new \Exception('Payment was not approved');
            }

        } catch (\Exception $e) {
            Log::error('Payment success handling error: ' . $e->getMessage());
            return redirect()->route('payment.error')
                ->with('error', 'Payment processing failed. Please contact support.');
        }
    }

    /**
     * Handle cancelled PayPal payment
     */
    public function paymentCancel(Request $request, $orderId)
    {
        try {
            $order = Order::findOrFail($orderId);

            // Update order status to cancelled
            $order->update(['status' => 'Cancelled']);

            if ($order->order_type === 'service') {
                return redirect()->route('services.show', $order->service->slug ?? $order->service->id)
                    ->with('info', 'Payment was cancelled. You can try again anytime.');
            } else {
                return redirect()->route('products.show', $order->product->slug ?? $order->product->id)
                    ->with('info', 'Payment was cancelled. You can try again anytime.');
            }

        } catch (\Exception $e) {
            Log::error('Payment cancel handling error: ' . $e->getMessage());
            return redirect()->route('products')
                ->with('error', 'An error occurred. Please try again.');
        }
    }

    /**
     * Show payment success page
     */
    public function paymentSuccessPage($orderId)
    {
        $order = Order::with(['product', 'service', 'user'])->findOrFail($orderId);

        // Ensure the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.success', compact('order'));
    }

    /**
     * Show payment error page
     */
    public function paymentError()
    {
        return view('payment.error');
    }

    /**
     * Send automatic message to professional after service purchase
     */
    private function sendServicePurchaseMessage($order)
    {
        if ($order->order_type !== 'service' || !$order->service || !$order->service->professional_id) {
            return;
        }

        $service = $order->service;
        $client = $order->user;
        $professionalId = $service->professional_id;

        $subject = "New Service Order: {$service->title}";
        $content = "Hello,\n\n";
        $content .= "You have received a new service order from {$client->name}.\n\n";
        $content .= "Service: {$service->title}\n";
        $content .= "Order Amount: \${$order->price}\n";
        $content .= "Order ID: #{$order->id}\n\n";
        $content .= "The client is looking forward to working with you on this project. Please get in touch with them as soon as possible to discuss the project details and timeline.\n\n";
        $content .= "Payment for this order is currently being held securely and will be released to you once the client marks the order as completed.\n\n";
        $content .= "Best regards,\n";
        $content .= "The Hermosart Team";

        Message::create([
            'sender_id' => $client->id,
            'recipient_id' => $professionalId,
            'order_id' => $order->id,
            'subject' => $subject,
            'content' => $content,
            'is_read' => false
        ]);
    }
}
