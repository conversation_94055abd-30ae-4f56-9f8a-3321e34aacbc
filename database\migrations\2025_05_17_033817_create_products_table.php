<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->nullable();
            $table->decimal('price', 8, 2);
            $table->string('image');
            $table->text('description');
            $table->decimal('rating', 3, 1);
            $table->integer('sales');
            $table->string('category'); // Foreign key to categories.id
            $table->string('professional_id')->nullable();
            $table->foreign('professional_id')->references('id')->on('professionals')->onDelete('set null');

            // Add user_id for regular sellers (users with is_seller=true)
            $table->uuid('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->timestamps();

            $table->foreign('category')->references('id')->on('categories')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
