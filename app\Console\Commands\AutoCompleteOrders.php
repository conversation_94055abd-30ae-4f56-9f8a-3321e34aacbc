<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\Earning;

class AutoCompleteOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:auto-complete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto-complete orders that have been processing for more than 30 days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting auto-completion of orders...');

        // Find orders that should be auto-completed
        $ordersToComplete = Order::where('status', 'Processing')
            ->where('order_type', 'service')
            ->whereNotNull('auto_complete_date')
            ->where('auto_complete_date', '<=', now())
            ->with(['service', 'user'])
            ->get();

        if ($ordersToComplete->isEmpty()) {
            $this->info('No orders found for auto-completion.');
            return;
        }

        $completedCount = 0;

        foreach ($ordersToComplete as $order) {
            try {
                DB::transaction(function () use ($order) {
                    // Update order status
                    $order->update([
                        'status' => 'Completed',
                        'completed_at' => now()
                    ]);

                    // Update earnings status to release payment
                    $earnings = Earning::where('order_id', $order->id)->get();
                    foreach ($earnings as $earning) {
                        $earning->update(['status' => 'Paid']);
                    }
                });

                $this->info("Auto-completed order #{$order->id} for service: {$order->service->title}");
                $completedCount++;

            } catch (\Exception $e) {
                $this->error("Failed to auto-complete order #{$order->id}: {$e->getMessage()}");
            }
        }

        $this->info("Auto-completion completed. {$completedCount} orders were processed.");
    }
}
